<view class="u-radio data-v-643b3322" style="{{$root.s0}}"><view data-event-opts="{{[['tap',[['toggle',['$event']]]]]}}" class="{{['u-radio__icon-wrap','data-v-643b3322',iconClass]}}" style="{{$root.s1}}" bindtap="__e"><u-icon class="u-radio__icon-wrap__icon data-v-643b3322" vue-id="058ad445-1" name="checkbox-mark" size="{{elIconSize}}" color="{{iconColor}}" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['onClickLabel',['$event']]]]]}}" class="u-radio__label data-v-643b3322" style="{{'font-size:'+($root.g0)+';'}}" bindtap="__e"><slot></slot></view></view>