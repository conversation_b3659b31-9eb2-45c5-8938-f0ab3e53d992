<template>
	<view class="order-list-page">
		<!-- 订单状态 -->
		<view class="order-list-header">
			<view class="one-status" v-for="(item, index) in orderTypes" :key="index"
				:class="index == tabIndex ? 'active' : ''" @click="onClickItem(item, index)">{{ item.name }}</view>
		</view>
		<!-- 订单列表 -->
		<view class="order-list">
			<view class="each-order" v-for="(item, index) of orderList" :key="index">
				<view class="header">
					<view class="order-no-status">
						<view class="no">
							<view>订单编：{{item.orderNo}}</view>
							<view @click="onCopy(item.orderNo)" style="display: flex;margin-left: 20rpx;"><image
									style="width: 22rpx;height: 22rpx;" src="/static/img/shop/shop-copy.png" alt="" />
							</view>
						</view>
						<view class="status">{{item.orderStatus | formatState}}</view>
					</view>
				</view>
				<view class="good-detail">
					<view class="product_info">
						<view class="prodct_left">
							<image style="width:164rpx;height: 164rpx;" :src="item.goodsImg"></image>
						</view>
						<view class="product_center">
							<view style="font-size: 28rpx;line-height: 44rpx;" class="text-ellipsis_2">
								{{item.goodsName}}</view>
							<view style="display: flex;margin-top: 20rpx;">
								<view
									style="color: #61687C;font-size: 24rpx;background-color: #F2F4F7;text-align: center;padding: 6rpx 20rpx;border-radius: 6rpx;">
									{{item.specification}}g</view>
								<view></view>
							</view>
						</view>
						<view class="product_right">
							<view><text class="font_small">¥{{item.price}}</text></view>
							<view style="font-size: 24rpx;color: #9FA3B0;margin-top: 8rpx;text-align: right;">x1</view>
						</view>
					</view>
				</view>
				<view class="opera-btns">
					<view style="display: flex;justify-content: space-between;align-items: center;" v-if="item.orderStatus == '0'">
						<view style="display: flex;background-color: rgba(255, 0, 70, 0.05);align-items: center;padding: 4rpx 8rpx;border-radius: 8rpx;">
							<!-- 倒计时、创建时间 -->
							<view style="font-size: 24rpx;color: #FF0046;">支付剩余：</view>
							<u-count-down :timestamp="item.timestamp" font-size="24" bg-color="none" color="#FF0046" separator="zh" separator-size="24" separator-color="#FF0046"></u-count-down>
						</view>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<view class="each-btn cancel" @click="cancelOrderEvent(item)">取消订单</view>
							<view class="each-btn pay" @click="enterOrderDetailPage(item)">立即付款</view>
						</view>
					</view>
					<view style="display: flex;justify-content: space-between;align-items: center;" v-if="item.orderStatus == '1'">
						<view style="color: #9FA3B0;font-size: 24rpx;">
							<!-- 倒计时、创建时间 -->
							下单时间：{{item.createDate}}
						</view>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<view class="each-btn pay" @click="enterOrderDetailPage(item)">去核销</view>
						</view>
					</view>
					<view style="display: flex;justify-content: space-between;align-items: center;" v-if="['3','4','7'].includes(item.orderStatus)">
						<view style="color: #9FA3B0;font-size: 24rpx;">
							<!-- 下单时间：{{item.createDate}} -->
						</view>
						<view style="display: flex;justify-content: space-between;align-items: center;">
							<view class="each-btn pay" @click="enterOrderDetailPage(item)">查看订单</view>
						</view>
					</view>
				</view>
			</view>
			<u-loadmore :status="loadStatus" :load-text="loadText"></u-loadmore>
			<!-- #ifdef WEB -->
			<view class="page_bottom"></view>
			<!-- #endif -->
		</view>
	</view>
</template>

<script>
	const api = require('../../config/api')
	const util = require('../../utils/util')
	export default {
		name: 'orderList',
		data() {
			return {
				orderTypes: [{
						status: 5,
						name: '全部'
					},
					{
						status: 0,
						name: '待付款'
					},
					{
						status: 1,
						name: '待核销'
					},
					{
						status: 3,
						name: '已完成'
					}
				],
				tabIndex: '0',
				orderParams: {
					orderStatus: 5,
					page: 1,
					limit: 10
				},
				loadStatus: 'loading',
				loadText: {
					loadmore: '加载更多',
					loading: '努力加载中',
					nomore: '已经到底了'
				},
				activeColor: '#007aff',
				orderList: [],
				isLoadAll: false
			}
		},
		filters: {
			formatState: function(_state) {
				if (_state === '0') {
					return '待付款'
				} else if (_state === '1') {
					return '待核销'
				} else if (_state === '3') {
					return '已完成'
				} else if (_state === '4') {
					return '已取消'
				} else if (_state === '7') {
					return '已退款'
				}
			}
		},
		onPullDownRefresh() {
			this.isLoadAll = false
			this.orderParams.page = 1
			this.getOrderData()
		},
		onReachBottom () {
			console.log('加载中')
			if (!this.isLoadAll) {
				this.orderParams.page++
				this.getOrderData()
			}
		},
		onShow: function() {
			this.orderList=[]
			this.orderParams.page = 1
			this.getOrderData()
		},
		methods: {
			// 获取订单列表
			getOrderData() {
				this.loadStatus = 'loading'
				util.request(api.myOrderUrl, this.orderParams, 'POST').then((res) => {
					console.log(res)
					if (res.code !== 0) {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
					} else {
						let records = res.data.records
						for (let i = 0; i < records.length; i++) {
							// records[i].createDate = records[i].createDate.replace(/-/g,'/')
							// console.log(records[i].createDate)
							let nowTime = new Date()
							// let startTime = new Date(records[i].createDate);
							let endTime = new Date(records[i].createDate)
							let nowSeconds = ( nowTime.getTime()) / 1000 ; // 开始时间转换为秒
							// let startSeconds = startTime.getTime() / 1000; // 开始时间转换为秒
							let endSeconds = (endTime.getTime()+ 30 * 60 * 1000) / 1000; // 结束时间转换为秒
							let timestamp = endSeconds - nowSeconds; // 持续时间（秒）
							records[i].timestamp = timestamp
						}
						this.orderList = this.orderList.concat(records || [])
						this.isLoadAll = this.orderParams.page >= res.data.pages //4
						this.loadStatus = 'nomore'
					}
				})
			},
			// 切换订单状态tab
			onClickItem(item, inx) {
				this.tabIndex = inx
				this.orderParams.orderStatus = item.status
				this.orderParams.page = 1
				this.orderList = []
				this.getOrderData()
			},
			// 取消订单
			cancelOrderEvent(item) {
				util.request(api.cancelOrderUrl + item.orderNo, {}, 'POST').then((res) => {
					console.log(res)
					if (res.code !== 0) {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
					} else {
						uni.showToast({
							title: '取消订单成功',
							icon: 'none'
						})
						this.orderList=[]
						this.orderParams.page = 1
						this.getOrderData()
					}
				})
			},
			onCopy(orderNo) {
				uni.setClipboardData({
					data: orderNo,
					success: function() {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						})
					}
				});
			},
			// 查看订单详情
			enterOrderDetailPage(item) {
				uni.navigateTo({
					url: '/pages/order/orderDetail?orderNo=' + item.orderNo
				})
			},
			// 去付款
			toPayMoney(x, item) {}
		}
	}
</script>


<style lang="scss">
	page{
		background-color: #F7F7F7;
	}
	.order-list-page {
		height: 100%;

		.type-tabs {
			.segmented-control__text {
				font-size: 24rpx;
			}
		}
	}
</style>
<style lang="scss" scoped>
	.order-list-page {
		.order-list-header {
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			background-color: #FFFFFF;
			.one-status {
				padding: 10rpx 0;
				font-size: 28rpx;
				position: relative;
				color: #9FA3B0;

				&.active {
					color: #171B25;

					&:after {
						content: '';
						display: block;
						width: 100%;
						height: 2px;
						background-color: #BBA186;
						position: absolute;
						bottom: 0;
						left: 0;
						transition: all 0.3s;
					}
				}
			}
		}

		.order-list {
			// width: 710rpx;
			// margin: 0 auto;
			padding: 20rpx;
			// background-color: #ececec;
			// height: calc(100% - 100rpx);
			overflow: auto;

			.each-order {
				background-color: #ffffff;
				padding: 20rpx;
				margin-bottom: 20rpx;
				border-radius: 10rpx;

				.header {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.order-no-status {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;
						// padding-bottom: 20rpx;
					}

					.no {
						display: flex;
						align-items: center;
						color: #171B25;
						font-size: 28rpx;
					}

					.status {
						color: #FF0046;
						font-size: 28rpx;
					}
				}

				.good-detail {
					border-bottom: 1rpx solid #F7F7F7;
					.product_info {
						// margin-top: 24rpx;
						display: flex;
						padding: 24rpx;
						background-color: #FFFFFF;
						border-radius: 10rpx;
						.prodct_left {
							display: flex;
							border-radius: 10rpx;
							overflow: hidden;
						}

						.product_center {
							flex: 1;
							margin-left: 20rpx;
						}

						.product_right {
							margin-left: 32rpx;

							.font_small {
								font-size: 36rpx;
								color: #171B25;
								font-weight: 600;
							}
						}
					}
				}
				.opera-btns {
					margin-top: 20rpx;
					.each-btn {
						padding: 10rpx 20rpx;
						font-size: 24rpx;
						border: 1px solid #ececec;
						border-radius: 200rpx;
						margin-left: 20rpx;
						&.pay {
							color: #FFFFFF;
							background-color: #BBA186;
						}
					}
				}
			}
		}
	}
</style>