{"version": 3, "sources": ["webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-popup/u-popup.vue?3d6f", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-popup/u-popup.vue?fe6b", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-popup/u-popup.vue?f236", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-popup/u-popup.vue?0698", "uni-app:///node_modules/uview-ui/components/u-popup/u-popup.vue", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-popup/u-popup.vue?88c0", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-popup/u-popup.vue?34d0"], "names": ["name", "props", "show", "type", "default", "mode", "mask", "length", "zoom", "safeAreaInsetBottom", "maskCloseAble", "customStyle", "value", "popup", "borderRadius", "zIndex", "closeable", "closeIcon", "closeIconPos", "closeIconColor", "closeIconSize", "width", "height", "negativeTop", "maskCustomStyle", "duration", "data", "visibleSync", "showDrawer", "timer", "closeFromInner", "computed", "style", "transform", "centerStyle", "uZindex", "watch", "mounted", "methods", "getUnitValue", "maskClick", "close", "modeCenterClose", "open", "change"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgD5qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,gBAuBA;EACAA;EACAC;IACA;AACA;AACA;IACAC;MACAC;MACAC;IACA;IACA;AACA;AACA;IACAC;MACAF;MACAC;IACA;IACA;AACA;AACA;IACAE;MACAH;MACAC;IACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;EACA;EACAsB;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAA;UACAX;UACAC;UACAW;QACA;MACA;QACAD;UACAX;UACAC;UACAW;QACA;MACA;MACAD;MACA;MACA;QACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;QAAA;QAEA;QACAA;MACA;MACA;MACA;IACA;IACA;IACAE;MACA;MACAF;MACA;MACAA;MACAA;MACAA;MACA;QACAA;QACA;QACAA;MACA;MACA;IACA;IACA;IACAG;MACA;IACA;EACA;EACAC;IACAxB;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAyB;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA,kDACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;MACA;QAEA;UACA;UACA;QACA;MAQA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC/UA;AAAA;AAAA;AAAA;AAAuwC,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACA3xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-popup/u-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-popup.vue?vue&type=template&id=52d4ddd1&scoped=true&\"\nvar renderjs\nimport script from \"./u-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./u-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-popup.vue?vue&type=style&index=0&id=52d4ddd1&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"52d4ddd1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-popup/u-popup.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=template&id=52d4ddd1&scoped=true&\"", "var components\ntry {\n  components = {\n    uMask: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-mask/u-mask\" */ \"uview-ui/components/u-mask/u-mask.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.visibleSync\n    ? _vm.__get_style([\n        _vm.customStyle,\n        {\n          zIndex: _vm.uZindex - 1,\n        },\n      ])\n    : null\n  var s1 = _vm.visibleSync ? _vm.__get_style([_vm.style]) : null\n  var s2 =\n    _vm.visibleSync && _vm.mode == \"center\"\n      ? _vm.__get_style([_vm.centerStyle])\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"visibleSync\" :style=\"[customStyle, {\r\n\t\tzIndex: uZindex - 1\r\n\t}]\" class=\"u-drawer\" hover-stop-propagation>\r\n\t\t<u-mask :duration=\"duration\" :custom-style=\"maskCustomStyle\" :maskClickAble=\"maskCloseAble\" :z-index=\"uZindex - 2\" :show=\"showDrawer && mask\" @click=\"maskClick\"></u-mask>\r\n\t\t<view\r\n\t\t\tclass=\"u-drawer-content\"\r\n\t\t\t@tap=\"modeCenterClose(mode)\"\r\n\t\t\t:class=\"[\r\n\t\t\t\tsafeAreaInsetBottom ? 'safe-area-inset-bottom' : '',\r\n\t\t\t\t'u-drawer-' + mode,\r\n\t\t\t\tshowDrawer ? 'u-drawer-content-visible' : '',\r\n\t\t\t\tzoom && mode == 'center' ? 'u-animation-zoom' : ''\r\n\t\t\t]\"\r\n\t\t\************************\r\n\t\t\******************\r\n\t\t\t:style=\"[style]\"\r\n\t\t>\r\n\t\t\t<view class=\"u-mode-center-box\" @tap.stop.prevent @touchmove.stop.prevent v-if=\"mode == 'center'\" :style=\"[centerStyle]\">\r\n\t\t\t\t<u-icon\r\n\t\t\t\t\t@click=\"close\"\r\n\t\t\t\t\tv-if=\"closeable\"\r\n\t\t\t\t\tclass=\"u-close\"\r\n\t\t\t\t\t:class=\"['u-close--' + closeIconPos]\"\r\n\t\t\t\t\t:name=\"closeIcon\"\r\n\t\t\t\t\t:color=\"closeIconColor\"\r\n\t\t\t\t\t:size=\"closeIconSize\"\r\n\t\t\t\t></u-icon>\r\n\t\t\t\t<scroll-view class=\"u-drawer__scroll-view\" scroll-y=\"true\">\r\n\t\t\t\t\t<slot />\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view class=\"u-drawer__scroll-view\" scroll-y=\"true\" v-else>\r\n\t\t\t\t<slot />\r\n\t\t\t</scroll-view>\r\n\t\t\t<view @tap=\"close\" class=\"u-close\" :class=\"['u-close--' + closeIconPos]\">\r\n\t\t\t\t<u-icon\r\n\t\t\t\t\tv-if=\"mode != 'center' && closeable\"\r\n\t\t\t\t\t:name=\"closeIcon\"\r\n\t\t\t\t\t:color=\"closeIconColor\"\r\n\t\t\t\t\t:size=\"closeIconSize\"\r\n\t\t\t\t></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * popup 弹窗\r\n * @description 弹出层容器，用于展示弹窗、信息提示等内容，支持上、下、左、右和中部弹出。组件只提供容器，内部内容由用户自定义\r\n * @tutorial https://www.uviewui.com/components/popup.html\r\n * @property {String} mode 弹出方向（默认left）\r\n * @property {Boolean} mask 是否显示遮罩（默认true）\r\n * @property {Stringr | Number} length mode=left | 见官网说明（默认auto）\r\n * @property {Boolean} zoom 是否开启缩放动画，只在mode为center时有效（默认true）\r\n * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配（默认false）\r\n * @property {Boolean} mask-close-able 点击遮罩是否可以关闭弹出层（默认true）\r\n * @property {Object} custom-style 用户自定义样式\r\n * @property {Stringr | Number} negative-top 中部弹出时，往上偏移的值\r\n * @property {Numberr | String} border-radius 弹窗圆角值（默认0）\r\n * @property {Numberr | String} z-index 弹出内容的z-index值（默认1075）\r\n * @property {Boolean} closeable 是否显示关闭图标（默认false）\r\n * @property {String} close-icon 关闭图标的名称，只能uView的内置图标\r\n * @property {String} close-icon-pos 自定义关闭图标位置（默认top-right）\r\n * @property {String} close-icon-color 关闭图标的颜色（默认#909399）\r\n * @property {Number | String} close-icon-size 关闭图标的大小，单位rpx（默认30）\r\n * @event {Function} open 弹出层打开\r\n * @event {Function} close 弹出层收起\r\n * @example <u-popup v-model=\"show\"><view>出淤泥而不染，濯清涟而不妖</view></u-popup>\r\n */\r\nexport default {\r\n\tname: 'u-popup',\r\n\tprops: {\r\n\t\t/**\r\n\t\t * 显示状态\r\n\t\t */\r\n\t\tshow: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t/**\r\n\t\t * 弹出方向，left|right|top|bottom|center\r\n\t\t */\r\n\t\tmode: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'left'\r\n\t\t},\r\n\t\t/**\r\n\t\t * 是否显示遮罩\r\n\t\t */\r\n\t\tmask: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 抽屉的宽度(mode=left|right)，或者高度(mode=top|bottom)，单位rpx，或者\"auto\"\r\n\t\t// 或者百分比\"50%\"，表示由内容撑开高度或者宽度\r\n\t\tlength: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 'auto'\r\n\t\t},\r\n\t\t// 是否开启缩放动画，只在mode=center时有效\r\n\t\tzoom: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 是否开启底部安全区适配，开启的话，会在iPhoneX机型底部添加一定的内边距\r\n\t\tsafeAreaInsetBottom: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否可以通过点击遮罩进行关闭\r\n\t\tmaskCloseAble: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 用户自定义样式\r\n\t\tcustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {};\r\n\t\t\t}\r\n\t\t},\r\n\t\tvalue: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 此为内部参数，不在文档对外使用，为了解决Picker和keyboard等融合了弹窗的组件\r\n\t\t// 对v-model双向绑定多层调用造成报错不能修改props值的问题\r\n\t\tpopup: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 显示显示弹窗的圆角，单位rpx\r\n\t\tborderRadius: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tzIndex: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 是否显示关闭图标\r\n\t\tcloseable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 关闭图标的名称，只能uView的内置图标\r\n\t\tcloseIcon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'close'\r\n\t\t},\r\n\t\t// 自定义关闭图标位置，top-left为左上角，top-right为右上角，bottom-left为左下角，bottom-right为右下角\r\n\t\tcloseIconPos: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'top-right'\r\n\t\t},\r\n\t\t// 关闭图标的颜色\r\n\t\tcloseIconColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#909399'\r\n\t\t},\r\n\t\t// 关闭图标的大小，单位rpx\r\n\t\tcloseIconSize: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: '30'\r\n\t\t},\r\n\t\t// 宽度，只对左，右，中部弹出时起作用，单位rpx，或者\"auto\"\r\n\t\t// 或者百分比\"50%\"，表示由内容撑开高度或者宽度，优先级高于length参数\r\n\t\twidth: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 高度，只对上，下，中部弹出时起作用，单位rpx，或者\"auto\"\r\n\t\t// 或者百分比\"50%\"，表示由内容撑开高度或者宽度，优先级高于length参数\r\n\t\theight: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 给一个负的margin-top，往上偏移，避免和键盘重合的情况，仅在mode=center时有效\r\n\t\tnegativeTop: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 遮罩的样式，一般用于修改遮罩的透明度\r\n\t\tmaskCustomStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 遮罩打开或收起的动画过渡时间，单位ms\r\n\t\tduration: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 250\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tvisibleSync: false,\r\n\t\t\tshowDrawer: false,\r\n\t\t\ttimer: null,\r\n\t\t\tcloseFromInner: false, // value的值改变，是发生在内部还是外部\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\t// 根据mode的位置，设定其弹窗的宽度(mode = left|right)，或者高度(mode = top|bottom)\r\n\t\tstyle() {\r\n\t\t\tlet style = {};\r\n\t\t\t// 如果是左边或者上边弹出时，需要给translate设置为负值，用于隐藏\r\n\t\t\tif (this.mode == 'left' || this.mode == 'right') {\r\n\t\t\t\tstyle = {\r\n\t\t\t\t\twidth: this.width ? this.getUnitValue(this.width) : this.getUnitValue(this.length),\r\n\t\t\t\t\theight: '100%',\r\n\t\t\t\t\ttransform: `translate3D(${this.mode == 'left' ? '-100%' : '100%'},0px,0px)`\r\n\t\t\t\t};\r\n\t\t\t} else if (this.mode == 'top' || this.mode == 'bottom') {\r\n\t\t\t\tstyle = {\r\n\t\t\t\t\twidth: '100%',\r\n\t\t\t\t\theight: this.height ? this.getUnitValue(this.height) : this.getUnitValue(this.length),\r\n\t\t\t\t\ttransform: `translate3D(0px,${this.mode == 'top' ? '-100%' : '100%'},0px)`\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\tstyle.zIndex = this.uZindex;\r\n\t\t\t// 如果用户设置了borderRadius值，添加弹窗的圆角\r\n\t\t\tif (this.borderRadius) {\r\n\t\t\t\tswitch (this.mode) {\r\n\t\t\t\t\tcase 'left':\r\n\t\t\t\t\t\tstyle.borderRadius = `0 ${this.borderRadius}rpx ${this.borderRadius}rpx 0`;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'top':\r\n\t\t\t\t\t\tstyle.borderRadius = `0 0 ${this.borderRadius}rpx ${this.borderRadius}rpx`;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'right':\r\n\t\t\t\t\t\tstyle.borderRadius = `${this.borderRadius}rpx 0 0 ${this.borderRadius}rpx`;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'bottom':\r\n\t\t\t\t\t\tstyle.borderRadius = `${this.borderRadius}rpx ${this.borderRadius}rpx 0 0`;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t}\r\n\t\t\t\t// 不加可能圆角无效\r\n\t\t\t\tstyle.overflow = 'hidden';\r\n\t\t\t}\r\n\t\t\tif(this.duration) style.transition = `all ${this.duration / 1000}s linear`;\r\n\t\t\treturn style;\r\n\t\t},\r\n\t\t// 中部弹窗的特有样式\r\n\t\tcenterStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\tstyle.width = this.width ? this.getUnitValue(this.width) : this.getUnitValue(this.length);\r\n\t\t\t// 中部弹出的模式，如果没有设置高度，就用auto值，由内容撑开高度\r\n\t\t\tstyle.height = this.height ? this.getUnitValue(this.height) : 'auto';\r\n\t\t\tstyle.zIndex = this.uZindex;\r\n\t\t\tstyle.marginTop = `-${this.$u.addUnit(this.negativeTop)}`;\r\n\t\t\tif (this.borderRadius) {\r\n\t\t\t\tstyle.borderRadius = `${this.borderRadius}rpx`;\r\n\t\t\t\t// 不加可能圆角无效\r\n\t\t\t\tstyle.overflow = 'hidden';\r\n\t\t\t}\r\n\t\t\treturn style;\r\n\t\t},\r\n\t\t// 计算整理后的z-index值\r\n\t\tuZindex() {\r\n\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.popup;\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tvalue(val) {\r\n\t\t\tif (val) {\r\n\t\t\t\tthis.open();\r\n\t\t\t} else if(!this.closeFromInner) {\r\n\t\t\t\tthis.close();\r\n\t\t\t}\r\n\t\t\tthis.closeFromInner = false;\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\t// 组件渲染完成时，检查value是否为true，如果是，弹出popup\r\n\t\tthis.value && this.open();\r\n\t},\r\n    methods: {\r\n\t\t// 判断传入的值，是否带有单位，如果没有，就默认用rpx单位\r\n\t\tgetUnitValue(val) {\r\n\t\t\tif(/(%|px|rpx|auto)$/.test(val)) return val;\r\n\t\t\telse return val + 'rpx'\r\n\t\t},\r\n\t\t// 遮罩被点击\r\n\t\tmaskClick() {\r\n\t\t\tthis.close();\r\n\t\t},\r\n\t\tclose() {\r\n\t\t\t// 标记关闭是内部发生的，否则修改了value值，导致watch中对value检测，导致再执行一遍close\r\n\t\t\t// 造成@close事件触发两次\r\n\t\t\tthis.closeFromInner = true;\r\n\t\t\tthis.change('showDrawer', 'visibleSync', false);\r\n\t\t},\r\n\t\t// 中部弹出时，需要.u-drawer-content将居中内容，此元素会铺满屏幕，点击需要关闭弹窗\r\n\t\t// 让其只在mode=center时起作用\r\n\t\tmodeCenterClose(mode) {\r\n\t\t\tif (mode != 'center' || !this.maskCloseAble) return;\r\n\t\t\tthis.close();\r\n\t\t},\r\n\t\topen() {\r\n\t\t\tthis.change('visibleSync', 'showDrawer', true);\r\n\t\t},\r\n\t\t// 此处的原理是，关闭时先通过动画隐藏弹窗和遮罩，再移除整个组件\r\n\t\t// 打开时，先渲染组件，延时一定时间再让遮罩和弹窗的动画起作用\r\n\t\tchange(param1, param2, status) {\r\n\t\t\t// 如果this.popup为false，意味着为picker，actionsheet等组件调用了popup组件\r\n\t\t\tif (this.popup == true) {\r\n\t\t\t\tthis.$emit('input', status);\r\n\t\t\t}\r\n\t\t\tthis[param1] = status;\r\n\t\t\tif(status) {\r\n\t\t\t\t// #ifdef H5 || MP\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis[param2] = status;\r\n\t\t\t\t\tthis.$emit(status ? 'open' : 'close');\r\n\t\t\t\t}, 50);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5 || MP\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis[param2] = status;\r\n\t\t\t\t\tthis.$emit(status ? 'open' : 'close');\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t} else {\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis[param2] = status;\r\n\t\t\t\t\tthis.$emit(status ? 'open' : 'close');\r\n\t\t\t\t}, this.duration);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"../../libs/css/style.components.scss\";\r\n\r\n.u-drawer {\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: block;\r\n\t/* #endif */\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\toverflow: hidden;\r\n}\r\n\r\n.u-drawer-content {\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: block;\r\n\t/* #endif */\r\n\tposition: absolute;\r\n\tz-index: 1003;\r\n\ttransition: all 0.25s linear;\r\n}\r\n\r\n.u-drawer__scroll-view {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.u-drawer-left {\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tbackground-color: #ffffff;\r\n}\r\n\r\n.u-drawer-right {\r\n\tright: 0;\r\n\ttop: 0;\r\n\tbottom: 0;\r\n\tbackground-color: #ffffff;\r\n}\r\n\r\n.u-drawer-top {\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground-color: #ffffff;\r\n}\r\n\r\n.u-drawer-bottom {\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground-color: #ffffff;\r\n}\r\n\r\n.u-drawer-center {\r\n\t@include vue-flex;\r\n\tflex-direction: column;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\ttop: 0;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\topacity: 0;\r\n\tz-index: 99999;\r\n}\r\n\r\n.u-mode-center-box {\r\n\tmin-width: 100rpx;\r\n\tmin-height: 100rpx;\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: block;\r\n\t/* #endif */\r\n\tposition: relative;\r\n\tbackground-color: #ffffff;\r\n}\r\n\r\n.u-drawer-content-visible.u-drawer-center {\r\n\ttransform: scale(1);\r\n\topacity: 1;\r\n}\r\n\r\n.u-animation-zoom {\r\n\ttransform: scale(1.15);\r\n}\r\n\r\n.u-drawer-content-visible {\r\n\ttransform: translate3D(0px, 0px, 0px) !important;\r\n}\r\n\r\n.u-close {\r\n\tposition: absolute;\r\n\tz-index: 3;\r\n}\r\n\r\n.u-close--top-left {\r\n\ttop: 30rpx;\r\n\tleft: 30rpx;\r\n}\r\n\r\n.u-close--top-right {\r\n\ttop: 30rpx;\r\n\tright: 30rpx;\r\n}\r\n\r\n.u-close--bottom-left {\r\n\tbottom: 30rpx;\r\n\tleft: 30rpx;\r\n}\r\n\r\n.u-close--bottom-right {\r\n\tright: 30rpx;\r\n\tbottom: 30rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=style&index=0&id=52d4ddd1&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-popup.vue?vue&type=style&index=0&id=52d4ddd1&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360227071\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}