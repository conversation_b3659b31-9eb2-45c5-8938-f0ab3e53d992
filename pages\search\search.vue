<template>
	<view class="page">
		<view>
			<view style="background-color: #FFFFFF;padding: 10rpx 24rpx 24rpx 24rpx;">
				<u-search placeholder="搜索商品" v-model="keyword" :clearabled="true" :show-action="true" action-text="搜索" @custom="getIndexinfo" @search="getIndexinfo"></u-search>
			</view>
			<view class="order-list-header">
				<view class="one-status" v-for="(item, index) in orderTypes" :key="index"
					:class="index == tabIndex ? 'active' : ''" @click="onClickItem(item, index)">
					<view>{{ item.name }}</view>
					<view v-if="index==2" style="margin-left: 6rpx;" @click.stop="onPrice">
						<view :class="upPrice?'activescolor activescolor-active':'activescolor'"></view>
						<view :class="!upPrice?'defaultscolor':'defaultscolor defaultscolor-active'"></view>
					</view>
				</view>
			</view>
		</view>
		<view class="shop_content">
			<!-- Waterfall 瀑布流 -->
			<u-waterfall v-model="goodsList" ref="uWaterfall">
				<template v-slot:left="{leftList}">
					<view class="shop_list" v-for="(item, index) in leftList" :key="index" @click="goShop(item)">
						<!-- 这里编写您的内容，item为您传递给v-model的数组元素 -->
						<!-- <u-lazy-load :height="340" threshold="100" :image="item.goodsImg" :index="index"></u-lazy-load> -->
						<image style="width: 100%;height: 340rpx;" :src="item.goodsImg" alt="" />
						<view class="demo-title text-ellipsis_2">
							{{item.goodsName}}
						</view>
						<view class="shop-price">
							<view class="shop-price-num">
								<view><text>¥</text><text style="font-size: 32rpx;">{{item.price}}</text><text style="font-weight: 400;color: #9FA3B0;margin-left: 10rpx;">{{item.shop}}</text></view>
							</view>
							<view class="shop_add">
								<image style="width: 32rpx;height: 32rpx;" src="/static/img/index/index-add-circle.png" alt="" srcset="" />
							</view>
						</view>
					</view>
				</template>
				<template v-slot:right="{rightList}">
					<view class="shop_list" v-for="(item, index) in rightList" :key="index" @click="goShop(item)">
						<!-- 这里编写您的内容，item为您传递给v-model的数组元素 -->
						<!-- <u-lazy-load :height="340" threshold="100" :image="item.goodsImg" :index="index"></u-lazy-load> -->
						<image style="width: 100%;height: 340rpx;" :src="item.goodsImg" alt="" />
						<view class="demo-title text-ellipsis_2">
							{{item.goodsName}}
						</view>
						<view class="shop-price">
							<view class="shop-price-num">
								<view><text>¥</text><text style="font-size: 32rpx;">{{item.price}}</text><text style="font-weight: 400;color: #9FA3B0;margin-left: 10rpx;">{{item.shop}}</text></view>
							</view>
							<view class="shop_add">
								<image style="width: 32rpx;height: 32rpx;" src="/static/img/index/index-add-circle.png" alt="" srcset="" />
							</view>
						</view>
					</view>
				</template>
			</u-waterfall>
		</view>
		<!-- #ifdef MP-WEIXIN -->
		<u-loadmore :margin-top="goodsList.length?'':50" :status="loadStatus" :load-text="loadText"></u-loadmore>
		<!-- #endif -->
		<!-- #ifdef WEB -->
		<u-loadmore :status="loadStatus" :load-text="loadText" @loadmore="addRandomData"></u-loadmore>
		<!-- #endif -->
	</view>
</template>

<script>
	const api = require('../../config/api')
	const util = require('../../utils/util')
	export default {
		data() {
			return {
				loadStatus: 'loading',
				loadText: {
					loadmore: '加载更多',
					loading: '努力加载中',
					nomore: '已经到底了'
				},
				orderTypes: [{
						status: 0,
						name: '综合'
					},
					{
						status: 1,
						name: '销量'
					},
					{
						status: 2,
						name: '价格'
					}
				],
				tabIndex: '0',
				goodsList: [],
				storeInfo: {},
				keyword: '',
				shanxuan: '0',
				upPrice: false
			}
		},
		async onLoad() {
			await this.getIndexinfo()
		},
		async onShow() {
			
		},

		methods: {
			goShop(item) {
				uni.navigateTo({
					url: '/pages/indexChild/GoodsDetails/GoodsDetails?goodsId='+item.id
				})
			},
			onClickItem(item, inx) {
				this.tabIndex = inx
				this.shanxuan = item.status
				this.upPrice = false
				this.$refs.uWaterfall.clear();
				this.getIndexinfo()
			},
			onPrice() {
				this.tabIndex = 2
				this.upPrice = !this.upPrice
				this.shanxuan = this.upPrice?3:2
				this.$refs.uWaterfall.clear();
				this.getIndexinfo()
			},
			async getIndexinfo() {
				let that = this;
				const res = await util.request(api.goodsListUrl, {
					name: that.keyword,
					sx: that.shanxuan
				}, 'POST')
				// console.log(res)
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				} else {
					that.goodsList = res.data
					// that.$set(that, 'goodsList', res.data)
					console.log(that.goodsList)
					that.loadStatus = 'nomore'
				}
			}
		}
	}
</script>
<style>
	page{
		background-color: #F9F5F2;
	}
</style>
<style lang="scss" scoped>
.page {
	// padding-bottom: 20rpx;
}
.activescolor {
  width: 0;
  height: 0;
  border: 4px solid;
  border-color: transparent transparent #9FA3B0 transparent;
  &.activescolor-active{
  	  border-color: transparent transparent #171B25 transparent;
  }
}
.defaultscolor {
	margin-top: 4rpx;
  width: 0;
  height: 0;
  border: 4px solid;
  border-color: #171B25 transparent transparent transparent;
  opacity: 0.7;
  &.defaultscolor-active{
	  border-color: #9FA3B0 transparent transparent transparent;
  }
}
.order-list-header {
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-around;
	background-color: #FFFFFF;
	.one-status {
		display: flex;
		padding: 10rpx 0;
		font-size: 28rpx;
		position: relative;
		color: #9FA3B0;

		&.active {
			color: #171B25;
		}
	}
}
.shop_content{
	padding-left: 24rpx;
	.shop_list{
		border-radius: 10rpx 10rpx 0 0;
		overflow: hidden;
		background-color: #fff;
		margin: 24rpx 0;
		margin-right: 24rpx;
		.demo-title {
			font-size: 28rpx;
			margin: 16rpx;
			color: #171B25;
		}
		.shop-price{
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10rpx 24rpx 20rpx 24rpx;
			.shop-price-num{
				display: flex;
				align-items: center;
				color: #FF0046;
				font-size: 24rpx;
				font-weight: 600;
			}
			.shop_add{
				display: flex;
			}
		}
	}
}
</style>