<template>
	<view class="page">
		<view style="background-color: #FFFFFF;padding: 10rpx 24rpx 24rpx 24rpx;">
			<u-search placeholder="请输入提货点" v-model="keyword" :clearabled="true" :show-action="true" action-text="搜索" @custom="onSearch" @search="onSearch"></u-search>
		</view>
		<view class="store_content" v-for="item in storeList" :key="item.id">
			<view class="store_img">
				<image :src="item.img" style="width: 160rpx;height: 160rpx;" alt="" />
			</view>
			<view class="store_name">
				<view style="margin-top: 10rpx;font-size: 28rpx;">{{item.name}}</view>
				<view style="display: flex;justify-content: space-between;align-items: center;color: #61687C;font-size: 24rpx;margin-top: 10rpx;">
					<view style="display: flex;">
						<image style="width: 28rpx;" mode="widthFix" src="/static/img/shop/shop-clock.png" alt="" /><text style="margin-left: 10rpx;">{{item.openTime}}</text>
					</view>
					<view style="display: flex;" v-if="type==1">
						<view class="store_round" @click="onCall(item.phone)"><image style="width: 28rpx;" mode="widthFix" src="/static/img/shop/shop-calling.png" alt="" /></view>
						<view class="store_round" @click="onStoreInfo(item)"><image style="width: 28rpx;" mode="widthFix" src="/static/img/shop/shop-user.png" alt="" /></view>
					</view>
					<view @click="onChoose(item)" style="display: flex;" v-else>
						<view style="display: flex;"><image style="width: 48rpx;height: 48rpx;" :src="`/static/img/shop/shop_round${storeInfo.id==item.id?'_a':''}.png`" alt="" /></view>
					</view>
				</view>
				<view style="display: flex;color: #61687C;font-size: 24rpx;margin-top: 10rpx;"><image style="width: 28rpx;" mode="widthFix" src="/static/img/shop/shop-location.png" alt="" /><text style="margin-left: 10rpx;">{{item.address}}</text></view>
			</view>
		</view>
		<!-- #ifdef MP-WEIXIN -->
		<u-loadmore :margin-top="storeList.length?'':50" :status="loadStatus" :load-text="loadText"></u-loadmore>
		<!-- #endif -->
		<!-- #ifdef WEB -->
		<u-loadmore :status="loadStatus" :load-text="loadText" @loadmore="addRandomData"></u-loadmore>
		<!-- #endif -->
		<u-popup v-model="show" mode="center" :border-radius="14" length="90%" :close-icon-color="'#FFFFFF'" :closeable="true">
			<view>
				<view class="gold_new">
					<view class="gold_new_title">门店信息</view>
				</view>
				<view style="padding: 0 32rpx;">
					<view class="gold_price_show">
						<view style="font-size: 28rpx;">联系人：</view>
						<view><text style="font-size: 32rpx;color: #BBA186;">{{storeInfo.userName}}</text></view>
					</view>
					<view class="gold_price_show">
						<view style="font-size: 28rpx;">联系方式：</view>
						<view><text style="font-size: 32rpx;color: #BBA186;">{{storeInfo.phone}}</text></view>
					</view>
				</view>
				<view class="goods_detail_footer">
					<view @click="show=false">我知道了</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	const api = require('../../config/api');
	const util = require('../../utils/util');
	export default {
		data() {
			return {
				storeList: [],
				page: 1,
				limit: 10,
				show: false,
				storeContact: {},
				storeInfo: {},
				type: '',
				loadStatus: 'loading',
				loadText: {
					loadmore: '加载更多',
					loading: '努力加载中',
					nomore: '已经到底了'
				},
				isLoadAll: false,
				keyword: ''
			}
		},
		onLoad(options) {
			console.log(options)
			this.type = options.type||''
			// uni.setNavigationBarTitle({
			// 	title: options.type==1?'门店查询':'选择门店'
			// });
			// this.getUserInfo()
		},
		async onShow() {
			let that = this
			this.storeList=[]
			this.page = 1
			await this.getStoreList()
			uni.getStorage({
				key: 'store_info',
				success: function (res) {
					that.storeInfo = res.data
				}
			});
		},
		onReachBottom () {
			if (!this.isLoadAll) {
				this.page++
				this.getStoreList()
			}
		},
		methods: {
			async onSearch() {
				this.storeList=[]
				this.page = 1
				await this.getStoreList()
			},
			async getStoreList() {
				let that = this;
				that.loadStatus = 'loading'
				const res = await util.request(
					api.storeListUrl, {
						limit: that.limit,
						page: that.page,
						name: that.keyword
					},
					'POST'
				);
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})
			
				} else {
					that.$nextTick(()=>{
						that.storeList = that.storeList.concat(res.data.records)
					})
					that.isLoadAll = that.page >= res.data.pages //4
					that.loadStatus = 'nomore'
				}
			},
			onChoose(item){
				this.storeInfo = {...item}
				uni.setStorage({
					key: 'store_info',
					data: item,
					success: function () {
						setTimeout(()=>{
							uni.navigateBack()
						},600)
					}
				});
			},
			onCall(phone) {
				uni.makePhoneCall({
					phoneNumber: phone //仅为示例
				});
			},
			onStoreInfo(item) {
				let that = this
				that.storeInfo = {
					userName: item.userName,
					phone: item.phone
				}
				setTimeout(()=>{
					that.show = true
				},100)
			}
		}
	}
</script>
<style>
	page {
		background-color: #F8F8F8;
	}

</style>
<style lang="scss" scoped>
.page {
	// padding: 24rpx 24rpx 0 24rpx;
	.store_content{
		display: flex;
		margin: 24rpx;
		padding: 24rpx;
		border-radius: 10rpx;
		background-color: #FFFFFF;
		.store_img{
			display: flex;
			border-radius: 10rpx;
			overflow: hidden;
		}
		.store_name{
			flex: 1;
			margin-left: 24rpx;
		}
	}
}
	.gold_new{
			padding: 40rpx 0;
			background: linear-gradient(94deg, #8F8174 -2.53%, #C4B39F 131.45%);
			view{
				color: #FFFFFF;
				text-align: center;
				font-size: 24rpx;
			}
		.gold_new_title{
			font-size: 36rpx;
			// padding-top: 40rpx;
			padding-bottom: 20rpx;
		}
	}
		.gold_price_show{
			display: flex;
			justify-content: space-between;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #F1F2F5;
		}
		.goods_detail_footer {
			margin-top: 32rpx;
			width: 100%;
			height: 50px;
			display: flex;
			justify-content: center;
		}
		.goods_detail_footer>view {
			height: 84rpx;
			width: 80%;
			border-radius: 9999px;
			background-color: #BBA186;
			color: #FFFFFF;
			font-size: 28rpx;
			text-align: center;
			line-height: 84rpx;
		}
.store_round{
	width: 52rpx;
	height: 52rpx;
	border-radius: 50%;
	overflow: hidden;
	background-color: #F5F5F5;
	display: flex;
	align-items: center;
	margin-left: 24rpx;
	justify-content: center;
}
</style>