const formatTime = (date) => {
	const year = date.getFullYear();
	const month = date.getMonth() + 1;
	const day = date.getDate();
	const hour = date.getHours();
	const minute = date.getMinutes();
	const second = date.getSeconds();
	return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`;
};
const formatNumber = (n) => {
	n = n.toString();
	return n[1] ? n : `0${n}`;
};
function trimObjectStrings(obj) {  
  // 使用Object.keys()遍历对象的所有键  
  Object.keys(obj).forEach(key => {  
    // 检查属性值是否为字符串  
    if (typeof obj[key] === 'string') {  
      // 如果是字符串，则清除前后空格  
      obj[key] = obj[key].trim();  
    }  
    // 注意：这里不递归处理嵌套对象，如果需要，可以添加递归逻辑  
  });  
  // 返回修改后的对象（实际上，由于JavaScript中的对象是按引用传递的，所以不需要返回）  
  return obj; // 但为了函数调用的清晰性，还是返回了  
}  
// 封装request
function request(url, data = {}, method = 'GET') {
	return new Promise((resolve, reject) => {
		uni.request({
			url: url,
			data: data,
			method: method,
			header: {
				token: uni.getStorageSync('token')
			},
			success: (res) => {
				// console.log(res);
				if (res.statusCode == 200) {
					// 只要是501 提示 强制重定向到登录页面进行登录
					if (res.data.code == 401) {
						uni.reLaunch({
							url: '/pages/register/register'
						});
					} else {
						resolve(res.data);
					}
				} else {
					reject(res.data);
				}
			},
			fail: (err) => {
				// console.log(err);
				if (err.errno === 1001) {
					uni.showToast({
						title: '服务器无响应，请检查网络连接或稍后重试',
						icon: 'none'
					})
					// reject('服务器无响应，请检查网络连接或稍后重试');
				} else {
					reject(err);
				}
			}
		});
	});
}
import md5 from '@/utils/md5.js'
// import { generateRandomLetters } from '@/utils/number.js'
function generateRandomLetters() {
	const length = 6; // 你可以修改这个值来生成不同长度的随机字母字符串  
	const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'; // 包含所有字母的字符串  
	let result = '';
	const charactersLength = characters.length;
	for (let i = 0; i < length; i++) {
		result += characters.charAt(Math.floor(Math.random() * charactersLength));
	}
	return result; // 将生成的随机字母字符串赋值给data中的randomLetters  
}

function request2(url, data = {}, method = 'POST') {
	return new Promise((resolve, reject) => {
		let timestamp = Date.now();
		let nonceStr = generateRandomLetters();
		let apisecret = 123456789;
		// nonceStr: "sssssdssss",
		let str = timestamp + nonceStr + apisecret
		let sign = md5.hex_md5(str)
		uni.request({
			url: url,
			data: data,
			method: method,
			header: {
				token: uni.getStorageSync('token'),
				timestamp: timestamp,
				nonceStr: nonceStr,
				apisecret: 123456789,
				sign: sign,
			},
			success: (res) => {
				// console.log(res);
				if (res.statusCode == 200) {
					// 只要是501 提示 强制重定向到登录页面进行登录
					if (res.data.code == 401) {
						uni.navigateTo({
							url: '/pages/index/index'
						});
					} else {
						resolve(res.data);
					}
				} else {
					reject(res.data);
				}
			},
			fail: (err) => {
				// console.log(err);
				if (err.errno === 1001) {
					uni.showToast({
						title: '服务器无响应，请检查网络连接或稍后重试',
						icon: 'none'
					})
					// reject('服务器无响应，请检查网络连接或稍后重试');
				} else {
					reject(err);
				}
			}
		});
	});
}

function uploadFile(url, filePath, formData) {
	// console.log(url, filePath,formData);
	return new Promise((resolve, reject) => {
		let timestamp = Date.now();
		let nonceStr = generateRandomLetters();
		let apisecret = 123456789;
		// nonceStr: "sssssdssss",
		let str = timestamp + nonceStr + apisecret
		let sign = md5.hex_md5(str)
		uni.uploadFile({
			url: url,
			filePath: filePath,
			name: 'file',
			header: {
				token: uni.getStorageSync('token'),
				timestamp: timestamp,
				nonceStr: nonceStr,
				apisecret: 123456789,
				sign: sign
			}, // 设置请求的 header
			formData: formData,
			success: (res) => {
				if (res.statusCode == 200) {
					let data = JSON.parse(res.data)
					if (data.code == 0) {
						uni.hideLoading();
						resolve(data);
					} else {
						uni.showToast({
							title: data.msg,
							icon: 'none'
						})
						reject(data);
					}
				} else {
					reject(res.data);
				}
			},
			fail: (err) => {
				console.log(err);
			}
		})
	});
}
// 封装提示类
function showAlertToast(msg) {
	uni.showToast({
		title: msg,
		icon:'none'
	});
}

function processName(name) {
	// 检查字符串长度  
	if (typeof name !== 'string' || name.length === 0) {
		// 如果不是字符串或长度为0，则返回原始值或空字符串  
		return name || '';
	}
	if (name.length === 2) {
		return name[0] + '*';
	} else if (name.length >= 3) {
		const stars = '*';
		return name[0] + stars + name.slice(-1);
	} else {
		return name;
	}
}
function phoneSubstring(str) {
	// 检查字符串长度  
	if (typeof str !== 'string' || str.length === 0) {
		// 如果不是字符串或长度为0，则返回原始值或空字符串  
		return str || '';
	}
	 if (str.length < 7) {  
	        // 如果字符串长度小于7，直接返回原始字符串  
	        return str;  
	    }  
	const Statestr=str.substring(0,3);
	const Endstr = str.substring(str.length - 4);
	return Statestr + '****' + Endstr;
}
function banknumberSubstring(str) {
	// 检查字符串长度  
	if (typeof str !== 'string' || str.length === 0) {
		// 如果不是字符串或长度为0，则返回原始值或空字符串  
		return str || '';
	}
	 if (str.length < 8) {  
	        // 如果字符串长度小于7，直接返回原始字符串  
	        return str;  
	    }  
	const Statestr=str.substring(0,4);
	const Endstr = str.substring(str.length - 4);
	return Statestr + '*******' + Endstr;
}
function phoneSubstringfct(str) {
	// 检查字符串长度  
	if (typeof str !== 'string' || str.length === 0) {
		// 如果不是字符串或长度为0，则返回原始值或空字符串  
		return str || '';
	}
	 if (str.length < 8) {  
	        // 如果字符串长度小于7，直接返回原始字符串  
	        return str;  
	    }  
	const Statestr=str.substring(0,3);
	const Endstr = str.substring(str.length - 4);
	return Statestr + '*******' + Endstr;
}
const debounce = (fn, wait = 1000, immediately = true) => {
	let timer = null;  
	return function() {
		const that = this;  
		const args = arguments;  
	  
		// 如果存在定时器，则清除它  
		if (timer) clearTimeout(timer);  
	  
		if (immediately) {  
			// 立即执行函数  
			fn.apply(that, args);  
	  
			// 设置一个定时器来防止在wait毫秒内再次立即执行  
			timer = setTimeout(() => {  
				timer = null; // 清除定时器引用  
			}, wait);  
		} else {  
			// 设置一个定时器来在wait毫秒后执行函数  
			timer = setTimeout(() => {  
				fn.apply(that, args);  
				timer = null; // 在函数执行后清除定时器引用  
			}, wait);  
		}  
	}; 
}
function generateRandom13DigitNumber() {  
     let result = '';  
        // 生成一个10位的随机数字串，因为Math.random()不会生成10的倍数，所以最后要+1  
        for (let i = 0; i < 13; i++) {    
            result += String(Math.floor(Math.random() * 10));  
        }  
 
        result =  result + '.jpg';  
        return result;   
} 
 // function processFileTokens(fileTokens) {  
 //        return fileTokens.map(res => {  
 //             // 创建一个新的对象来存储结果  
 //             const result = {};  
       
 //             // 根据attachType分配attachUrl到不同的字段  
 //             switch (res.attachType) {  
 //                 case '01':  
 //                     result.type01Url = res.attachUrl;  
 //                     break;  
 //                 case '02':  
 //                     result.type02Url = res.attachUrl;  
 //                     break;  
 //                 case '05':  
 //                     result.type05Url = res.attachUrl;  
 //                     break;  
 //                 default:  
                  
 //                     break;  
 //             }  
       
 //             // 返回处理后的对象  
 //             return result;  
 //         }); 
 // } \
module.exports = {
	formatTime,
	request,
	showAlertToast,
	request2,
	uploadFile,
	processName,
	debounce,
	phoneSubstring,
	banknumberSubstring,
	generateRandom13DigitNumber,
	trimObjectStrings,
	phoneSubstringfct

};