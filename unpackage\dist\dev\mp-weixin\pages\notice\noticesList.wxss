
page {
	background-color: #fafafa;
}

@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.notice-list .each-notice.data-v-71fe8b4a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #F7F7F7;
  background-color: #fff;
}
.notice-list .each-notice .left.data-v-71fe8b4a {
  flex: 1;
}
.notice-list .each-notice .left .notice-title.data-v-71fe8b4a {
  flex: 1;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.notice-list .each-notice .left .notice-title .notice-name.data-v-71fe8b4a {
  color: #333;
  margin-right: 20rpx;
}
.notice-list .each-notice .left .notice-title .notice-content.data-v-71fe8b4a {
  color: #a7a7a7;
  font-size: 24rpx;
  margin-top: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.notice-list .each-notice .left .notice-time.data-v-71fe8b4a {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}
.notice-list .each-notice .right.data-v-71fe8b4a {
  width: 40rpx;
}
