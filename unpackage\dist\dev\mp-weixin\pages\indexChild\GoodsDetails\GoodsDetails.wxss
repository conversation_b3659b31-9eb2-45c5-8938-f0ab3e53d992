@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-5d37e7c3 {
  background-color: #F5F5F5;
}
.goods_content.data-v-5d37e7c3 {
  padding: 24rpx 24rpx 0 24rpx;
}
.goods_content .goods_padding.data-v-5d37e7c3 {
  padding: 24rpx;
  background-color: #FFFFFF;
  border-radius: 10rpx;
}
.goods_name.data-v-5d37e7c3 {
  margin-top: 18rpx;
  font-size: 32rpx;
  font-weight: 600;
}
.goods_desc.data-v-5d37e7c3 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  color: #9FA3B0;
  font-size: 24rpx;
}
.goods_price.data-v-5d37e7c3 {
  color: #FF0046;
  font-size: 48rpx;
}
.goods_price > text.data-v-5d37e7c3:first-child {
  font-weight: 600;
}
.goods_params.data-v-5d37e7c3 {
  margin-top: 20rpx;
  padding: 0 24rpx;
  background-color: #FFFFFF;
  border-radius: 10rpx;
}
.goods_params .goods_specifica.data-v-5d37e7c3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E5E6EB;
  padding: 24rpx 0;
}
.tips_item.data-v-5d37e7c3 {
  margin-top: 12rpx;
  width: 100rpx;
  text-align: center;
  padding: 6rpx 0;
  border-radius: 4rpx;
  background-color: rgba(255, 0, 70, 0.05);
  color: #FF0046;
  font-size: 22rpx;
}
.goods_detail_concent.data-v-5d37e7c3 {
  overflow-x: hidden;
  padding-bottom: 170rpx;
}
.goods_detail_footer.data-v-5d37e7c3 {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 170rpx;
  display: flex;
  justify-content: center;
  background-color: #FFFFFF;
  padding-top: 6rpx;
}
.goods_detail_footer view.data-v-5d37e7c3 {
  height: 84rpx;
  width: 90%;
  border-radius: 9999px;
  background-color: #BBA186;
  color: #FFFFFF;
  font-size: 28rpx;
  text-align: center;
  line-height: 84rpx;
}
