<template>
	<view class="page">
		<view style="background-color: #FFFFFF;padding: 10rpx 24rpx 24rpx 24rpx;">
			<u-search placeholder="请输入邀请人" v-model="keyword" :clearabled="true" :show-action="true" action-text="搜索" @custom="onSearch" @search="onSearch"></u-search>
		</view>
		<view class="store_content" v-for="item in storeList" :key="item.id" @click="onChoose(item)">
			<view>
				<view style="margin-top: 10rpx;font-size: 28rpx;">员工姓名：{{item.name}}</view>
				<view style="margin-top: 10rpx;font-size: 28rpx;">部门编号：{{item.num || '-'}}</view>
				<view style="margin-top: 10rpx;font-size: 28rpx;">邀请码：{{item.code || '-'}}</view>
			</view>
			<view style="display: flex;justify-content: space-between;align-items: center;color: #61687C;font-size: 24rpx;margin-top: 10rpx;">
				<view style="display: flex;">
					<view style="display: flex;"><image style="width: 48rpx;height: 48rpx;" :src="`/static/img/shop/shop_round${storeInfo.id==item.id?'_a':''}.png`" alt="" /></view>
				</view>
			</view>
		</view>
		<u-loadmore :status="loadStatus" :load-text="loadText"></u-loadmore>
	</view>
</template>

<script>
	const api = require('../../../config/api');
	const util = require('../../../utils/util');
	export default {
		data() {
			return {
				storeList: [],
				page: 1,
				limit: 10,
				show: false,
				storeContact: {},
				storeInfo: {},
				type: '',
				loadStatus: 'loading',
				loadText: {
					loadmore: '加载更多',
					loading: '努力加载中',
					nomore: '已经到底了'
				},
				isLoadAll: false,
				keyword: ''
			}
		},
		onLoad(options) {
			console.log(options)
			this.storeId = options.storeId||''
			// uni.setNavigationBarTitle({
			// 	title: options.type==1?'门店查询':'选择门店'
			// });
			// this.getUserInfo()
		},
		async onShow() {
			let that = this
			this.orderList=[]
			this.page = 1
			await this.getStoreList()
		},
		onReachBottom () {
			if (!this.isLoadAll) {
				this.page++
				this.getStoreList()
			}
		},
		methods: {
			async onSearch() {
				this.storeList=[]
				this.page = 1
				await this.getStoreList()
			},
			async getStoreList() {
				let that = this;
				that.loadStatus = 'loading'
				const res = await util.request(
					api.userListUrl, {
						limit: that.limit,
						page: that.page,
						id: that.storeId,
						name: that.keyword
					},
					'POST'
				);
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})
			
				} else {
					that.storeList = that.storeList.concat(res.data.records)
					that.isLoadAll = that.page >= res.data.pages //4
					that.loadStatus = 'nomore'
				}
			},
			onChoose(item){
				this.storeInfo = {...item}
				setTimeout(()=>{
					// 1. 获取当前页面栈实例（此时最后一个元素为当前页）
					let pages = getCurrentPages()
					// 2. 想要得到上一页面的实例需要 -2；返回上上页面的实例就 -3，以此类推
					let prevPage = pages[pages.length - 2]
					// 3. 给上一页面实例绑定getUser()方法和参数（注意是$vm）
					prevPage.$vm.getUser(this.storeInfo)
					// console.log(prevPage)
					uni.navigateBack()
				},600)
			},
			onCall(phone) {
				uni.makePhoneCall({
					phoneNumber: phone //仅为示例
				});
			},
			onStoreInfo(item) {
				let that = this
				that.storeInfo = {
					userName: item.userName,
					phone: item.phone
				}
				setTimeout(()=>{
					that.show = true
				},100)
			}
		}
	}
</script>
<style>
	page {
		background-color: #F8F8F8;
	}

</style>
<style lang="scss" scoped>
.page {
	// padding: 0 24rpx 0 24rpx;
	.store_content{
		display: flex;
		justify-content: space-between;
		margin: 24rpx;
		padding: 24rpx;
		border-radius: 10rpx;
		background-color: #FFFFFF;
		.store_img{
			display: flex;
			border-radius: 10rpx;
			overflow: hidden;
		}
		.store_name{
			display: flex;
			justify-content: space-between;
			margin-left: 24rpx;
		}
	}
}
	.gold_new{
			padding: 40rpx 0;
			background: linear-gradient(94deg, #8F8174 -2.53%, #C4B39F 131.45%);
			view{
				color: #FFFFFF;
				text-align: center;
				font-size: 24rpx;
			}
		.gold_new_title{
			font-size: 36rpx;
			// padding-top: 40rpx;
			padding-bottom: 20rpx;
		}
	}
		.gold_price_show{
			display: flex;
			justify-content: space-between;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #F1F2F5;
		}
		.goods_detail_footer {
			margin-top: 32rpx;
			width: 100%;
			height: 50px;
			display: flex;
			justify-content: center;
		}
		.goods_detail_footer>view {
			height: 84rpx;
			width: 80%;
			border-radius: 9999px;
			background-color: #BBA186;
			color: #FFFFFF;
			font-size: 28rpx;
			text-align: center;
			line-height: 84rpx;
		}
.store_round{
	width: 52rpx;
	height: 52rpx;
	border-radius: 50%;
	overflow: hidden;
	background-color: #F5F5F5;
	display: flex;
	align-items: center;
	margin-left: 24rpx;
	justify-content: center;
}
</style>