
page{
	background-color: #F9F5F2;
}

@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.activescolor.data-v-4cedc0c6 {
  width: 0;
  height: 0;
  border: 4px solid;
  border-color: transparent transparent #9FA3B0 transparent;
}
.activescolor.activescolor-active.data-v-4cedc0c6 {
  border-color: transparent transparent #171B25 transparent;
}
.defaultscolor.data-v-4cedc0c6 {
  margin-top: 4rpx;
  width: 0;
  height: 0;
  border: 4px solid;
  border-color: #171B25 transparent transparent transparent;
  opacity: 0.7;
}
.defaultscolor.defaultscolor-active.data-v-4cedc0c6 {
  border-color: #9FA3B0 transparent transparent transparent;
}
.order-list-header.data-v-4cedc0c6 {
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #FFFFFF;
}
.order-list-header .one-status.data-v-4cedc0c6 {
  display: flex;
  padding: 10rpx 0;
  font-size: 28rpx;
  position: relative;
  color: #9FA3B0;
}
.order-list-header .one-status.active.data-v-4cedc0c6 {
  color: #171B25;
}
.shop_content.data-v-4cedc0c6 {
  padding-left: 24rpx;
}
.shop_content .shop_list.data-v-4cedc0c6 {
  border-radius: 10rpx 10rpx 0 0;
  overflow: hidden;
  background-color: #fff;
  margin: 24rpx 0;
  margin-right: 24rpx;
}
.shop_content .shop_list .demo-title.data-v-4cedc0c6 {
  font-size: 28rpx;
  margin: 16rpx;
  color: #171B25;
}
.shop_content .shop_list .shop-price.data-v-4cedc0c6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 24rpx 20rpx 24rpx;
}
.shop_content .shop_list .shop-price .shop-price-num.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  color: #FF0046;
  font-size: 24rpx;
  font-weight: 600;
}
.shop_content .shop_list .shop-price .shop_add.data-v-4cedc0c6 {
  display: flex;
}
