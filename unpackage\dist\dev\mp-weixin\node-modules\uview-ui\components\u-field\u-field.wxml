<view class="{{['u-field','data-v-578c626d',(borderTop)?'u-border-top':'',(borderBottom)?'u-border-bottom':'']}}"><view class="{{['u-field-inner','data-v-578c626d',type=='textarea'?'u-textarea-inner':'','u-label-postion-'+labelPosition]}}"><view class="{{['u-label','data-v-578c626d',required?'u-required':'']}}" style="{{'justify-content:'+(justifyContent)+';'+('flex:'+(labelPosition=='left'?'0 0 '+labelWidth+'rpx':'1')+';')}}"><block wx:if="{{icon}}"><view class="u-icon-wrap data-v-578c626d"><u-icon class="u-icon data-v-578c626d" vue-id="9dbf4ff2-1" size="32" custom-style="{{iconStyle}}" name="{{icon}}" color="{{iconColor}}" bind:__l="__l"></u-icon></view></block><slot name="icon"></slot><text class="{{['u-label-text','data-v-578c626d',this.$slots.icon||icon?'u-label-left-gap':'']}}">{{label}}</text></view><view class="fild-body data-v-578c626d"><view class="u-flex-1 u-flex data-v-578c626d" style="{{$root.s0}}"><block wx:if="{{type=='textarea'}}"><textarea class="u-flex-1 u-textarea-class data-v-578c626d" style="{{$root.s1}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" disabled="{{disabled}}" maxlength="{{inputMaxlength}}" focus="{{focus}}" autoHeight="{{autoHeight}}" fixed="{{fixed}}" data-event-opts="{{[['input',[['onInput',['$event']]]],['blur',[['onBlur',['$event']]]],['focus',[['onFocus',['$event']]]],['confirm',[['onConfirm',['$event']]]],['tap',[['fieldClick',['$event']]]]]}}" value="{{value}}" bindinput="__e" bindblur="__e" bindfocus="__e" bindconfirm="__e" bindtap="__e"></textarea></block><block wx:else><input class="u-flex-1 u-field__input-wrap data-v-578c626d" style="{{$root.s2}}" type="{{type}}" password="{{password||this.type==='password'}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" disabled="{{disabled}}" maxlength="{{inputMaxlength}}" focus="{{focus}}" confirmType="{{confirmType}}" data-event-opts="{{[['focus',[['onFocus',['$event']]]],['blur',[['onBlur',['$event']]]],['input',[['onInput',['$event']]]],['confirm',[['onConfirm',['$event']]]],['tap',[['fieldClick',['$event']]]]]}}" value="{{value}}" bindfocus="__e" bindblur="__e" bindinput="__e" bindconfirm="__e" bindtap="__e"/></block></view><block wx:if="{{clearable&&value!=''&&focused}}"><u-icon class="u-clear-icon data-v-578c626d" vue-id="9dbf4ff2-2" size="{{clearSize}}" name="close-circle-fill" color="#c0c4cc" data-event-opts="{{[['^click',[['onClear']]]]}}" bind:click="__e" bind:__l="__l"></u-icon></block><view class="u-button-wrap data-v-578c626d"><slot name="right"></slot></view><block wx:if="{{rightIcon}}"><u-icon class="u-arror-right data-v-578c626d" style="{{$root.s3}}" vue-id="9dbf4ff2-3" name="{{rightIcon}}" color="#c0c4cc" size="26" data-event-opts="{{[['^click',[['rightIconClick']]]]}}" bind:click="__e" bind:__l="__l"></u-icon></block></view></view><block wx:if="{{errorMessage!==false&&errorMessage!=''}}"><view class="u-error-message data-v-578c626d" style="{{'padding-left:'+(labelWidth+'rpx')+';'}}">{{errorMessage}}</view></block></view>