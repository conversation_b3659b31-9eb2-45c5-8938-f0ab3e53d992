<template>
	<view class="page">
		<!-- #ifdef MP-WEIXIN -->
		<view style="position: absolute;left: 0;top: 0;right: 0;z-index: 5;">
			<u-navbar background="transparent" :back-icon-color="'#FFFFFF'" :border-bottom="false"></u-navbar>
		</view>
		<!-- #endif -->
		<view class="img">
			<u-swiper :height="750" borderRadius="0" imgMode="widthFix" name="imgUrl" :list="Goodsitem.goodsImg" mode="none"></u-swiper>
		</view>
		<view class="goods_content">
			<view class="goods_padding">
				<view class="goods_price">
					<text><text style="font-size: 32rpx;">¥</text>{{Goodsitem.price}}</text>
				</view>
				<view class="tips_item"  v-for="(item,index) in Goodsitem.goodsDescribe" :key="index">{{item}}</view>
				<view class="goods_name">{{Goodsitem.goodsName}}</view>
				<view class="goods_desc">
					<view>已成交 {{Goodsitem.sales}} 件</view>
					<view>剩余 {{Goodsitem.inventory}} 件</view>
				</view>
			</view>
			<view class="goods_params">
				<view class="goods_specifica">
					<view style="display: flex;">
						<view style="color: #61687C;">保障</view>
						<view style="margin-left: 24rpx;display: flex;color: #171B25;">
							<view style="display: flex;align-items: center;margin-right: 32rpx;" v-for="item in Goodsitem.baozhang">
								<view style="display: flex;"><image style="width: 25rpx;height: 25rpx;" src="/static/img/shop/shop_tick-circle.png" alt="" srcset="" /></view>
								<view style="margin-left: 8rpx;">{{item}}</view>
							</view>
						</view>
					</view>
					<view class="right-icon"></view>
				</view>
				<view class="goods_specifica" style="border: none;color: #171B25;">
					<view style="display: flex;">
						<view style="color: #61687C;">规格</view>
						<view style="margin-left: 24rpx;display: flex;">
							{{Goodsitem.specification}}g
						</view>
					</view>
					<view class="right-icon"></view>
				</view>
			</view>
		</view>
		<view style="padding: 30rpx 0">
			<u-divider half-width="260" border-color="#9FA3B0" bg-color="#F5F5F5">
				<image style="width: 102rpx;height: 25rpx;" mode="widthFix" src="/static/img/shop/shop_detail_title.png" alt="" srcset="" />
			</u-divider>
		</view>
		<view class="goods_detail_concent">
			<rich-text :nodes="Goodsitem.goodsDetailInfo"></rich-text>
		</view>
		<view class="goods_detail_footer">
			<view @click="goPay">立即购买</view>
		</view>
	</view>
</template>

<script>
	import number from "../../../utils/number.js";
	const api = require('../../../config/api');
	const util = require('../../../utils/util');
	export default {
		data() {
			return {
				number: number, //声明number属性并赋值为引入的number模块
				goodsId: '',
				Goodsitem: {},
				show: false,
				goodsNum: 1
			}
		},
		onLoad(o) {
			this.goodsId = o.goodsId
			this.getGoodsdetail();
		},
		onShow() {
			// this.$nextTick(()=>{
			// 	console.log(this.$refs.uSwiper)
			// })
		},
		methods: {
			goPay() {
				uni.navigateTo({
					url: '/pages/indexChild/payment/payment?goodsId=' + this.Goodsitem.id + '&' + 'goodsNum=' +
						this.goodsNum,
				})
			},
			showPopup() {
				this.show = true
			},
			async getGoodsdetail() {
				uni.showLoading({
					title: '加载中'
				});
				const res = await util.request(
					api.goodsUrl + '/' + this.goodsId, {},
					'POST'
				);
				console.log(res);
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})

				} else {
					uni.hideLoading();
					res.data[0].goodsImg = res.data[0].goodsImg.split(',')
					res.data[0].goodsDetailInfo = res.data[0].goodsDetailInfo.replace('max-','')
					res.data[0].goodsDescribe = res.data[0].goodsDescribe?res.data[0].goodsDescribe.split(','):[]
					res.data[0].baozhang = ['品质保障','售后无忧']
					this.Goodsitem = res.data[0]
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.page{
		background-color: #F5F5F5;
	}
	.goods_content{
		padding: 24rpx 24rpx 0 24rpx;
		.goods_padding{
			padding: 24rpx;
			background-color: #FFFFFF;
			border-radius: 10rpx;
		}
	}
	.goods_name {
		margin-top: 18rpx;
		font-size: 32rpx;
		font-weight: 600;
	}
	.goods_desc{
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		color: #9FA3B0;
		font-size: 24rpx;
	}
	.goods_price {
		// padding: 20rpx 0;
		color: #FF0046;
		font-size: 48rpx;
	}

	.goods_price>text:first-child {
		font-weight: 600;
	}
	
	.goods_params{
		margin-top: 20rpx;
		padding: 0 24rpx;
		background-color: #FFFFFF;
		border-radius: 10rpx;
		.goods_specifica{
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1px solid #E5E6EB;
			padding: 24rpx 0;
		}
	}
	.tips_item {
		margin-top: 12rpx;
		width: 100rpx;
		text-align: center;
		padding: 6rpx 0;
		border-radius: 4rpx;
		background-color: rgba(255, 0, 70, 0.05);
		color: #FF0046;
		font-size: 22rpx;
	}

	.goods_detail_concent {
		overflow-x: hidden;
		padding-bottom: 170rpx;
	}

	.goods_detail_footer {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 170rpx;
		display: flex;
		justify-content: center;
		background-color: #FFFFFF;
		padding-top: 6rpx;
		view {
			height: 84rpx;
			width: 90%;
			border-radius: 9999px;
			background-color: #BBA186;
			color: #FFFFFF;
			font-size: 28rpx;
			text-align: center;
			line-height: 84rpx;
		}
	}

</style>