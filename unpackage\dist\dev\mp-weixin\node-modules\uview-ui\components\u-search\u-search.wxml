<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-search data-v-1a326067" style="{{'margin:'+(margin)+';'}}" bindtap="__e"><view class="u-content data-v-1a326067" style="{{'background-color:'+(bgColor)+';'+('border-radius:'+(shape=='round'?'100rpx':'10rpx')+';')+('border:'+(borderStyle)+';')+('height:'+(height+'rpx')+';')}}"><view class="u-icon-wrap data-v-1a326067"><u-icon class="u-clear-icon data-v-1a326067" vue-id="0a234466-1" size="{{30}}" name="{{searchIcon}}" color="{{searchIconColor?searchIconColor:color}}" bind:__l="__l"></u-icon></view><input class="u-input data-v-1a326067" style="{{$root.s0}}" confirm-type="search" disabled="{{disabled}}" focus="{{focus}}" maxlength="{{maxlength}}" placeholder-class="u-placeholder-class" placeholder="{{placeholder}}" placeholder-style="{{'color: '+placeholderColor}}" type="text" data-event-opts="{{[['blur',[['blur',['$event']]]],['confirm',[['search',['$event']]]],['input',[['inputChange',['$event']]]],['focus',[['getFocus',['$event']]]]]}}" value="{{value}}" bindblur="__e" bindconfirm="__e" bindinput="__e" bindfocus="__e"/><block wx:if="{{keyword&&clearabled&&focused}}"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="u-close-wrap data-v-1a326067" bindtap="__e"><u-icon class="u-clear-icon data-v-1a326067" vue-id="0a234466-2" name="close-circle-fill" size="34" color="#c0c4cc" bind:__l="__l"></u-icon></view></block></view><view data-event-opts="{{[['tap',[['custom',['$event']]]]]}}" class="{{['u-action','data-v-1a326067',showActionBtn||show?'u-action-active':'']}}" style="{{$root.s1}}" catchtap="__e">{{actionText}}</view></view>