<template>
	<view class="page">
			<!-- #ifdef MP-WEIXIN -->
			<u-navbar background="transparent" :border-bottom="false"></u-navbar>
			<!-- #endif -->
			<view style="font-size: 48rpx;font-weight: 600;padding-top: 84rpx;">验证码登录</view>
			<view style="font-size: 24rpx;color: #61687C;padding-top: 16rpx;">若该手机号未注册，我们将为您自动注册</view>
			<view style="padding-top: 96rpx;">
				<view class="item">
					<view class="account" style="position: relative">
						<input type="number" placeholder-class="inp-palcehoder" v-model="phone"
							placeholder="输入11位手机号" />
					</view>
				</view>
				<view class="item">
					<view class="account" style="position: relative">
						<input type="number" placeholder-class="inp-palcehoder" v-model="passwd"
							placeholder="请输入手机验证码" />
						<view v-if="!disabled" size="mini" class="phonecode" @click="getPhonCode">获取验证码</view>
						<view v-if="disabled" class="phonecode">{{ countdown }}秒后重新获取</view>
					</view>
				</view>
			</view>
			<view class="confimr" @click="handleLogin">确定并登录</view>
			<view class="manual" v-if="footer">
				<view>
					<radio class="radion" @click="checkedTap" color="#BBA186" value="r1" :checked="checked" />
					<text>我已阅读并同意</text>
					<text class="login-agree" @tap="toUserUsage">《注册协议》</text><text>和</text>
					<text class="login-agree" @tap="toPrivacy">《隐私协议》</text><br /><text style="margin-left: 36rpx;margin-top: 10rpx;">并使用本机号码登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const api = require('../../config/api');
	const util = require('../../utils/util');
	export default {
		data() {
			return {
				checked: false,
				srcs: '',
				//图片验证码src
				countdown: 60,
				// 初始倒计时时间，单位：秒
				disabled: false,
				imgCode: '',
				DeviceID: '',
				tenantId: '3',
				passwd: '',
				phone: '' ,//17753422520,
				code: '',
				defaultPhoneHeight: '', //屏幕默认高度
				nowPhoneHeight: '', //屏幕现在的高度
				footer: true

			};
		},
		mounted() {
			// #ifdef WEB
			//监听软键盘获取当前屏幕高度的事件
			this.defaultPhoneHeight = window.innerHeight
			window.onresize = () => {
				this.nowPhoneHeight = window.innerHeight
			}
			// #endif
		},
		watch:{
			// #ifdef WEB
			//软键盘弹起事件
			nowPhoneHeight(){
				if(this.defaultPhoneHeight != this.nowPhoneHeight){ //手机键盘被唤起
					this.footer = false
				}else{ //手机键盘被关闭
					this.footer = true
				}
			}
			// #endif
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			// this.refreshsrc();
		},
		async onShow() {
			// #ifdef WEB
			// 在回调页面取出code值
			const urlParams = new URLSearchParams(window.location.href.split('?')[1]);
			this.code = urlParams.get('code')
			console.log('code: ',urlParams.get('code'))
			if(urlParams.get('code')) return
			await this.nextSep()
			// #endif
		},

		methods: {
			nextSep() {
				// 构造微信授权链接
				const appid = "wxa56aa346588ae42f";
				const redirectUri = encodeURIComponent(`https://gold.xinxiangfu.cn/goldxcx/#/pages/register/register`); // 回调页面的URL
				const scope = "snsapi_base"; // snsapi_base 或 snsapi_userinfo，snsapi_userinfo可以拉起用户授权窗口
				const state = "STATE";
				const authUrl =
					`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`;
				window.location.href = authUrl;
			},
			// 去隐私协议
			toPrivacy() {
				uni.navigateTo({
					url: '/pages/Privacy/Privacy'
				});
			},

			// 去用户使用协议
			toUserUsage() {
				// uni.navigateTo({
				// 	url: '/pages/UserUsage/UserUsage'
				// });
			},
			toPrivacy() {},
			// 单选框
			checkedTap() {
				this.checked = !this.checked

			},
			// 登录
			async handleLogin() {
				if (!this.phone.trim()) {
					uni.showToast({
						title: '手机号不能为空',
						icon: 'none',
					})
					return;
				}
				if (!this.passwd.trim()) {
					uni.showToast({
						title: '验证码不能空',
						icon: 'none'
					});
					return;
				}
				if (!this.checked) {
					uni.showToast({
						title: '请同意隐私政策和注册协议',
						icon: 'none'
					});
					return;
				}
				// try {
					const res = await util.request(
						api.LoginUrl, {
							passwd: this.passwd,
							phone: this.phone,
							tenantId: this.tenantId,
							code: this.code
						},
						'POST'
					);
					if (res.code !== 0) {
						uni.hideLoading();
						setTimeout(() => {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}, 30)
			
						return;
					} else {
						uni.setStorageSync('token', res.data.token)
						setTimeout(() => { 
							uni.showToast({
								title: "登陆成功",
								icon: 'none'
							});
							
							uni.switchTab({
							    url: '/pages/index/index'
							});
						}, 30)
					}
			},

			// 获取图片验证码
			// async refreshsrc() {
			// 	const randomString = this.generateRandomString(6);
			// 	this.DeviceID = randomString
			// 	this.srcs = api.ImgCodeUrl + "?DeviceID=" + this.DeviceID;
			// },

			// 生成字符串
			generateRandomString(length) {
				const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
				let result = '';
				for (let i = 0; i < length; i++) {
					const randomIndex = Math.floor(Math.random() * characters.length);
					result += characters.charAt(randomIndex);
				}
				return result;
			},
			async getPhonCode() {
				if (!this.phone.trim()) {
					uni.showToast({
						title: '手机号不能为空',
						icon: 'none'
					});
					return;
				}
				const res = await util.request(
					api.PhoneCodeUrl, {
						"deviceID": this.DeviceID,
						"phone": this.phone,
						"tenantId": 3
					},
					'POST'
				);
				// console.log(res);
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
				} else {
					uni.showToast({
						title: '发送成功',
						icon: 'none'
					});

					let countdown = this.countdown;

					this.disabled = true

					let timer = setInterval(() => {
						countdown--;


						this.countdown = countdown
						// 倒计时结束
						if (countdown <= 0) {
							clearInterval(timer);
							// 恢复按钮可点击状态
							this.countdown = 60,
								this.disabled = false
						}
					}, 1000);
				}
			}
		}
	};
</script>
<style>
	.status_bar {
		height: var(--status-bar-height);
		width: 100%;
	}

	/* 单选框 */
	.manual {
		position: fixed;
		bottom: 100rpx;
		left: 0;
		right: 0;
		font-size: 22rpx;
		color: #9FA3B0;
		padding: 0 80rpx;
		display: flex;
		justify-content: center;
	}

	.radion {
		align-self: center;
		margin-top: 0rpx;
	}

	radio .wx-radio-input {
		border-radius: 50%;
		width: 24rpx;
		border: 2rpx solid #5e5e5f;
		height: 24rpx;
	}

	.login-agree {
		color: #BBA186;
	}
</style>
<style lang="scss" scoped>
	.page {
		padding: 1px;
		padding: 0 80rpx;
	}
	.item {
		height: 45px;
		margin-top: 15px;

	}
	.account {
		display: flex;
		box-sizing: border-box;
		font-size: 30rpx;
		align-items: center;
		border-bottom: 1px solid #F1F2F5;
	}

	.account input {
		// padding-left: 20rpx;
		// width: 80%;
		flex: 1;
		height: 60rpx;
	}


	/* 短信验证码 */
	.phonecode {
		height: 35px;
		padding: 0px 10px;
		text-align: center;
		line-height: 35px;
		color: #BBA186;
		font-size: 28rpx;
	}

	.confimr {
		height: 88rpx;
		font-size: 28rpx;
		margin-top: 80rpx;
		background-color: #BBA186;
		text-align: center;
		color: #fff;
		line-height: 88rpx;
		border-radius: 30px;
	}
</style>