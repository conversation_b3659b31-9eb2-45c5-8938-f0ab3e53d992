<template>
  <view class="notice-detail">
    <view class="title">{{data.title}}</view>
    <view class="date">{{data.createDate}}</view>
    <view class="content" v-html="data.content"></view>
  </view>
</template>

<script>
export default {
  name: 'noticeDetail',
  data() {
    return {
      data: `<div class="post-content_main-post-info__qCbZu"><div class="index_bbs-thread-comp__PC7_r bbs-thread-comp main-thread"><div class="thread-content-detail">    <p></p><div data-hupu-node="image"><p><p class="image-wrapper"><span class="img-wrapper-embedded" id="img-wrapper-embedded-0" style="width: auto; max-width: 100%;"><div class="lazyload-wrapper "><image src="https://i1.hoopchina.com.cn/news-editor/2025-3-20/15-31-12/57ad0a38-2319-43bb-a838-7384649a8061.png?x-oss-process=image/resize,w_800/format,webp" alt="" class="thread-img" style="width: auto; max-width: 100%; height: auto;"></div></span></p></p></div><p>虎扑03月20日讯 今日NBA常规赛，国王主场123-119战胜骑士。</p><p>赛后，国王主教练道格-克里斯蒂接受了记者采访，谈到了这场赢下东部第一的胜利。</p><p>记者提问：“在第一节球队只得到了15分，但之后球队拿出了上佳的表现，这中间发生了什么？”</p><p>克里斯蒂表示：“我知道骑士有非常多优秀的球员，像多诺万（米切尔），埃文（莫布利），但我们后三节打出了我们自己的篮球，萨克拉门托式的篮球。但我们穿上这身球员，这就是我们希望做到的。我给队员传递的信息是，团结在彼此周围，为彼此战斗。”</p><p>今日获胜后，国王战绩来到35胜33负，位列西部第9。</p> <p></p><br><div class="article-source">  来源： X</div><br><br><br></div><div class="seo-dom">    <p></p><div data-hupu-node="image"><p><image src="https://i1.hoopchina.com.cn/news-editor/2025-3-20/15-31-12/57ad0a38-2319-43bb-a838-7384649a8061.png?x-oss-process=image/resize,w_800/format,webp"></p></div><p>虎扑03月20日讯 今日NBA常规赛，国王主场123-119战胜骑士。</p><p>赛后，国王主教练道格-克里斯蒂接受了记者采访，谈到了这场赢下东部第一的胜利。</p><p>记者提问：“在第一节球队只得到了15分，但之后球队拿出了上佳的表现，这中间发生了什么？”</p><p>克里斯蒂表示：“我知道骑士有非常多优秀的球员，像多诺万（米切尔），埃文（莫布利），但我们后三节打出了我们自己的篮球，萨克拉门托式的篮球。但我们穿上这身球员，这就是我们希望做到的。我给队员传递的信息是，团结在彼此周围，为彼此战斗。”</p><p>今日获胜后，国王战绩来到35胜33负，位列西部第9。</p> <p></p><br><div class="article-source">  来源： X</div><br><br><br></div></div><div class="post-operate_post-operate-comp-wrapper___odBI"><div class="post-operate-comp main-operate"><div class="post-operate-comp-main"><div class="post-operate-comp-main-recommend hove-deep todo-list "><i class="iconfont icontuijian todo-list-icon"></i><span class="todo-list-text">推荐<!-- --> (2)</span></div><div class="post-operate-comp-main-reply todo-list"><i class="iconfont iconpinglun todo-list-icon"></i><span class="todo-list-text">评论<!-- --> (10)</span></div><div class="post-operate-comp-main-collect  todo-list"><i class="iconfont iconshoucang todo-list-icon"></i><span class="todo-list-text">收藏</span></div><div class="post-operate-comp-main-share todo-list"><i class="iconfont iconfenxiang todo-list-icon"></i><span class="todo-list-text">分享</span><div class="share-modal"><div class="prefix"></div><div class="ct"><div class="left-share"><div class="icons"><div class="icon-list"><i class="iconfont iconQQ icon-list-img"></i><p class="icon-list-name">QQ</p></div><div class="icon-list"><i class="iconfont iconQQkongjian icon-list-img"></i><p class="icon-list-name">QQ空间</p></div><div class="icon-list"><i class="iconfont iconxinlangweibo icon-list-img"></i><p class="icon-list-name">微博</p></div></div><div class="copy-board"><div class="copy-value">https://bbs.hupu.com/631253845.html?is_reflow=pc</div><div title="点击复制分享地址" class="copy-btn">复制</div></div></div><div class="right-qrcode"><p class="qr-tip">微信扫一扫分享</p><div class="qr-img"></div></div></div></div></div></div><div class="post-operate-comp-other"><div class="post-operate-comp-other-report todo-list"><i class="iconfont iconjubao1 todo-list-icon"></i><span class="todo-list-text">举报</span></div><div class="post-operate-comp-other-only-main todo-list"><i class="iconfont iconzhikanlouzhu todo-list-icon"></i><span class="todo-list-text">只看楼主</span></div></div></div></div></div>`
    }
  },
  onLoad() {
    this.data = uni.getStorageSync('noticeData') || {}
  },
  onUnload() {
    uni.removeStorageSync('noticeData')
  }
}
</script>

<style lang="scss">
.notice-detail {
  padding: 20rpx;
  background-color: #fff;
  .title {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  .date {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 20rpx;
  }
  .content {
    // font-size: 28rpx;
    // line-height: 1.5;
    // color: #333;
  }
}
</style>
