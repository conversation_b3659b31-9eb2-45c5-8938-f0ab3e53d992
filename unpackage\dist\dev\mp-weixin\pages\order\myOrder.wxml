<view class="order-list-page data-v-67aadf18"><view class="order-list-header data-v-67aadf18"><block wx:for="{{orderTypes}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onClickItem',['$0',index],[[['orderTypes','',index]]]]]]]}}" class="{{['one-status','data-v-67aadf18',index==tabIndex?'active':'']}}" bindtap="__e">{{item.name}}</view></block></view><view class="order-list data-v-67aadf18"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="each-order data-v-67aadf18"><view class="header data-v-67aadf18"><view class="order-no-status data-v-67aadf18"><view class="no data-v-67aadf18"><view class="data-v-67aadf18">{{"订单编："+item.$orig.orderNo}}</view><view data-event-opts="{{[['tap',[['onCopy',['$0'],[[['orderList','',index,'orderNo']]]]]]]}}" style="display:flex;margin-left:20rpx;" bindtap="__e" class="data-v-67aadf18"><image style="width:22rpx;height:22rpx;" src="/static/img/shop/shop-copy.png" alt class="data-v-67aadf18"></image></view></view><view class="status data-v-67aadf18">{{item.f0}}</view></view></view><view class="good-detail data-v-67aadf18"><view class="product_info data-v-67aadf18"><view class="prodct_left data-v-67aadf18"><image style="width:164rpx;height:164rpx;" src="{{item.$orig.goodsImg}}" class="data-v-67aadf18"></image></view><view class="product_center data-v-67aadf18"><view class="text-ellipsis_2 data-v-67aadf18" style="font-size:28rpx;line-height:44rpx;">{{''+item.$orig.goodsName}}</view><view style="display:flex;margin-top:20rpx;" class="data-v-67aadf18"><view style="color:#61687C;font-size:24rpx;background-color:#F2F4F7;text-align:center;padding:6rpx 20rpx;border-radius:6rpx;" class="data-v-67aadf18">{{''+item.$orig.specification+"g"}}</view><view class="data-v-67aadf18"></view></view></view><view class="product_right data-v-67aadf18"><view class="data-v-67aadf18"><text class="font_small data-v-67aadf18">{{"¥"+item.$orig.price}}</text></view><view style="font-size:24rpx;color:#9FA3B0;margin-top:8rpx;text-align:right;" class="data-v-67aadf18">x1</view></view></view></view><view class="opera-btns data-v-67aadf18"><block wx:if="{{item.$orig.orderStatus=='0'}}"><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-67aadf18"><view style="display:flex;background-color:rgba(255, 0, 70, 0.05);align-items:center;padding:4rpx 8rpx;border-radius:8rpx;" class="data-v-67aadf18"><view style="font-size:24rpx;color:#FF0046;" class="data-v-67aadf18">支付剩余：</view><u-count-down vue-id="{{'e49cf624-1-'+index}}" timestamp="{{item.$orig.timestamp}}" font-size="24" bg-color="none" color="#FF0046" separator="zh" separator-size="24" separator-color="#FF0046" class="data-v-67aadf18" bind:__l="__l"></u-count-down></view><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-67aadf18"><view data-event-opts="{{[['tap',[['cancelOrderEvent',['$0'],[[['orderList','',index]]]]]]]}}" class="each-btn cancel data-v-67aadf18" bindtap="__e">取消订单</view><view data-event-opts="{{[['tap',[['enterOrderDetailPage',['$0'],[[['orderList','',index]]]]]]]}}" class="each-btn pay data-v-67aadf18" bindtap="__e">立即付款</view></view></view></block><block wx:if="{{item.$orig.orderStatus=='1'}}"><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-67aadf18"><view style="color:#9FA3B0;font-size:24rpx;" class="data-v-67aadf18">{{'下单时间：'+item.$orig.createDate+''}}</view><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-67aadf18"><view data-event-opts="{{[['tap',[['enterOrderDetailPage',['$0'],[[['orderList','',index]]]]]]]}}" class="each-btn pay data-v-67aadf18" bindtap="__e">去核销</view></view></view></block><block wx:if="{{item.g0}}"><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-67aadf18"><view style="color:#9FA3B0;font-size:24rpx;" class="data-v-67aadf18"></view><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-67aadf18"><view data-event-opts="{{[['tap',[['enterOrderDetailPage',['$0'],[[['orderList','',index]]]]]]]}}" class="each-btn pay data-v-67aadf18" bindtap="__e">查看订单</view></view></view></block></view></view></block><u-loadmore vue-id="e49cf624-2" status="{{loadStatus}}" load-text="{{loadText}}" class="data-v-67aadf18" bind:__l="__l"></u-loadmore></view></view>