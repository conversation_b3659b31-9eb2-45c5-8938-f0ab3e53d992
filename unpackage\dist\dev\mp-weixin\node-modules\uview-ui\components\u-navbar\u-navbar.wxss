@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-navbar.data-v-75dad532 {
  width: 100%;
}
.u-navbar-fixed.data-v-75dad532 {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 991;
}
.u-status-bar.data-v-75dad532 {
  width: 100%;
}
.u-navbar-inner.data-v-75dad532 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  position: relative;
  align-items: center;
}
.u-back-wrap.data-v-75dad532 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  flex-grow: 0;
  padding: 14rpx 14rpx 14rpx 24rpx;
}
.u-back-text.data-v-75dad532 {
  padding-left: 4rpx;
  font-size: 30rpx;
}
.u-navbar-content-title.data-v-75dad532 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex: 1;
  position: absolute;
  left: 0;
  right: 0;
  height: 60rpx;
  text-align: center;
  flex-shrink: 0;
}
.u-navbar-centent-slot.data-v-75dad532 {
  flex: 1;
}
.u-title.data-v-75dad532 {
  line-height: 60rpx;
  font-size: 32rpx;
  flex: 1;
}
.u-navbar-right.data-v-75dad532 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.u-slot-content.data-v-75dad532 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}
