import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 先引入自定义主题
import './static/style/theme.scss'
// 再引入uView
import uView from "uview-ui";
// #ifdef WEB
import wx from 'jweixin-module';
Vue.use(wx);
// #endif
Vue.use(uView);

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif