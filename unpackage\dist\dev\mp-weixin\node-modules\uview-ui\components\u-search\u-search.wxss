@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-search.data-v-1a326067 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.u-content.data-v-1a326067 {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 18rpx;
  flex: 1;
}
.u-clear-icon.data-v-1a326067 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-input.data-v-1a326067 {
  flex: 1;
  font-size: 28rpx;
  line-height: 1;
  margin: 0 10rpx;
  color: #909399;
}
.u-close-wrap.data-v-1a326067 {
  width: 40rpx;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.u-placeholder-class.data-v-1a326067 {
  color: #909399;
}
.u-action.data-v-1a326067 {
  font-size: 28rpx;
  color: #303133;
  width: 0;
  overflow: hidden;
  transition: all 0.3s;
  white-space: nowrap;
  text-align: center;
}
.u-action-active.data-v-1a326067 {
  width: 80rpx;
  margin-left: 10rpx;
}
