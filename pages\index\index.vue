<template>
	<view class="page">
		<view class="status_bar">
			<view class="location_info" @click="location">
				<view>{{storeInfo.name||'祥杰金条专家'}}</view>
				<view style="display: flex;"><image style="width: 32rpx;height: 32rpx;" src="/static/img/index/index_location.png" alt="" /></view>
			</view>
			<view class="search_icon" @click="onSearch"><u-icon size="32" name="/static/img/index/search-normal.png"></u-icon></view>
		</view>
		<!-- 轮播图 -->
		<view class="index-concent-item">
			<u-swiper borderRadius="0" :height="868" name="imgUrl" :list="bannerlist" mode="none"></u-swiper>
			<!-- 金价 -->
<!-- 			<view :class="scrollTop<86?'location_info search_info active':'location_info search_info'">
				<view style="display: flex;justify-content: center;">
					<image style="width: 36rpx;" mode="widthFix" src="/static/img/index/index-gold-price.png" alt="" srcset="" />
				</view>
				<view>金价</view>
				<u-line length="30" direction="col" margin="0 6rpx" color="white" />
				<view style="display: flex;"><image style="width: 32rpx;height: 32rpx;" src="/static/img/index/search-normal.png" alt="" /></view>
			</view> -->
		</view>
		<view style="padding: 30rpx 0 6rpx 0;">
			<u-divider half-width="260" border-color="#BBA186" bg-color="#F5F5F5">
				<image style="width: 128rpx;height: 34rpx;" mode="widthFix" src="/static/img/index/index_tuijian.png" alt="" srcset="" />
			</u-divider>
		</view>
		<view class="shop_content">
			<!-- Waterfall 瀑布流 -->
			<u-waterfall v-model="goodsList">
				<template v-slot:left="{leftList}">
					<view class="shop_list" v-for="(item, index) in leftList" :key="index" @click="goShop(item)">
						<!-- 这里编写您的内容，item为您传递给v-model的数组元素 -->
						<!-- <u-lazy-load :height="340" threshold="100" :image="item.goodsImg" :index="index"></u-lazy-load> -->
						<image style="width: 100%;height: 340rpx;" :src="item.goodsImg" alt="" />
						<view class="demo-title text-ellipsis_2">
							{{item.goodsName}}
						</view>
						<view class="shop-price">
							<view class="shop-price-num">
								<view><text>¥</text><text style="font-size: 32rpx;">{{item.price}}</text><text style="font-weight: 400;color: #9FA3B0;margin-left: 10rpx;">{{item.shop}}</text></view>
							</view>
							<view class="shop_add">
								<image style="width: 32rpx;height: 32rpx;" src="/static/img/index/index-add-circle.png" alt="" srcset="" />
							</view>
						</view>
					</view>
				</template>
				<template v-slot:right="{rightList}">
					<view class="shop_list" v-for="(item, index) in rightList" :key="index" @click="goShop(item)">
						<!-- 这里编写您的内容，item为您传递给v-model的数组元素 -->
						<!-- <u-lazy-load threshold="100" :image="item.goodsImg" :index="index"></u-lazy-load> -->
						<image style="width: 100%;height: 340rpx;" :src="item.goodsImg" alt="" />
						<view class="demo-title text-ellipsis_2">
							{{item.goodsName}}
						</view>
						<view class="shop-price">
							<view class="shop-price-num">
								<view><text>¥</text><text style="font-size: 32rpx;">{{item.price}}</text><text style="font-weight: 400;color: #9FA3B0;margin-left: 10rpx;">{{item.shop}}</text></view>
							</view>
							<view class="shop_add">
								<image style="width: 32rpx;height: 32rpx;" src="/static/img/index/index-add-circle.png" alt="" srcset="" />
							</view>
						</view>
					</view>
				</template>
			</u-waterfall>
		</view>
		<!-- #ifdef MP-WEIXIN -->
		<u-loadmore :margin-top="goodsList.length?'':50" :status="loadStatus" :load-text="loadText"></u-loadmore>
		<!-- #endif -->
		<!-- #ifdef WEB -->
		<u-loadmore :status="loadStatus" :load-text="loadText" @loadmore="addRandomData"></u-loadmore>
		<view class="page_bottom"></view>
		<!-- #endif -->
		<view class="gold-price" @click="onGoldPrice">
			<view style="display: flex;justify-content: center;">
				<image style="width: 36rpx;height: 36rpx;" src="/static/img/index/index-gold-price.png" alt="" srcset="" />
			</view>
			<view style="font-size: 24rpx;text-align: center;margin-top: 4rpx;">金价</view>
		</view>
		<u-popup v-model="show" mode="center" :border-radius="14" length="90%" :close-icon-color="'#FFFFFF'" :closeable="true">
			<view>
				<view class="gold_new">
					<view class="gold_new_title">今日金价</view>
					<view>更新于 {{timeGold}}</view>
				</view>
				<view style="padding: 0 32rpx;">
					<view class="gold_price_show">
						<view style="font-size: 28rpx;">{{gyGold.remarks}}</view>
						<view><text style="font-size: 32rpx;color: #BBA186;">{{gyGold.configValue}}</text><text style="font-size: 24rpx;margin-left: 4rpx;">元/克</text></view>
					</view>
					<view class="gold_price_show">
						<view style="font-size: 28rpx;">{{tzGold.remarks}}</view>
						<view><text style="font-size: 32rpx;color: #BBA186;">{{tzGold.configValue}}</text><text style="font-size: 24rpx;margin-left: 4rpx;">元/克</text></view>
					</view>
				</view>
				<view class="goods_detail_footer">
					<view @click="show=false">我知道了</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import number from '../../utils/number.js'
	const api = require('../../config/api')
	const util = require('../../utils/util')
	export default {
		data() {
			return {
				number: number, //声明number属性并赋值为引入的number模块
				bannerlist: [], //轮播图
				loadStatus: 'loading',
				loadText: {
					loadmore: '加载更多',
					loading: '努力加载中',
					nomore: '已经到底了'
				},
				goodsList: [],
				storeInfo: {},
				show: false,
				tzGold: {},
				gyGold: {},
				timeGold: ''
			}
		},
		onLoad() {
			// this.upadetaApp();
			// this.addRandomData();
		},
		async onShow() {
			let that = this
			await this.getIndexinfo()
			uni.getStorage({
				key: 'store_info',
				success: function (res) {
					that.storeInfo = res.data
				}
			});
			// this.getNoticeList()
			// this.getInfoAmt()
		},
		// onReachBottom() {
		// 	this.addRandomData();
		// },
		// onPageScroll(e) {
		// 	let that = this;
		// 	clearTimeout(this.scrollTimer);
		// 	this.scrollTimer = setTimeout(function() {
		// 	  // 执行滚动相关逻辑（如调整界面状态）
		// 	  that.scrollTop = e.scrollTop
		// 	  // console.log('防抖触发', e.scrollTop);
		// 	}, 100); // 设置防抖时长为1000毫秒
		// },
		methods: {
			goShop(item) {
				uni.navigateTo({
					url: '/pages/indexChild/GoodsDetails/GoodsDetails?goodsId='+item.id
				})
			},
			location(){
				uni.navigateTo({
					url: "/pages/promotion/store"
				})
			},
			onSearch() {
				uni.navigateTo({
					url: "/pages/search/search"
				})
			},
			onGoldPrice(){
				this.show = true
			},
			async getIndexinfo() {
				const res = await util.request(api.indexInfoUrl, {}, 'POST')
				// console.log(res)
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				} else {
					this.bannerlist = res.data.imgList
					this.goodsList = res.data.goodsList
					this.gyGold = res.data.gyGold
					this.tzGold = res.data.tzGold
					this.timeGold = this.getCurrentDateTime() // 今日金价
					this.loadStatus = 'nomore'
				}
			},
			getCurrentDateTime() {
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
				const day = String(now.getDate()).padStart(2, '0');
				const hours = String(now.getHours()).padStart(2, '0');
				const minutes = String(now.getMinutes()).padStart(2, '0');
				const seconds = String(now.getSeconds()).padStart(2, '0');
				this.dateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			}
		}
	}
</script>
<style>
	page{
		background-color: #F9F5F2;
	}
</style>
<style lang="scss" scoped>
.page {
	padding-bottom: 20rpx;
}
/* #ifdef MP-WEIXIN */
.status_bar {
	position: absolute;
	z-index: 5;
	left: 0;
	top: 0;
	right: 0;
	margin-top: 98rpx;
	padding: 0 24rpx 10rpx 24rpx;
	font-size: 28rpx;
	color: #171B25;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.location_info{
		display: flex;
		align-items: center;
		height: 32px;
		padding: 5px 12px;
		gap: 4px;
		border-radius: 999px;
		background: rgba(255,255,255,0.3);
		color: #fff;
		font-size: 24rpx;
		backdrop-filter: blur(7.5px);
		opacity: 1;
		transition: opacity 0.3s ease-in;
	}
	.search_icon{
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		// border: 1rpx solid #E5E6EB;
		display: flex;
		justify-items: center;
		justify-content: center;
		margin-right: 25vw;
		background: rgba(255,255,255,0.3);
	}
}
/* #endif */
/* #ifdef WEB */
.status_bar {
	position: absolute;
	z-index: 5;
	left: 0;
	top: 0;
	right: 0;
	// margin-top: 98rpx;
	padding: 24rpx 24rpx 10rpx 24rpx;
	font-size: 28rpx;
	color: #171B25;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.location_info{
		display: flex;
		align-items: center;
		height: 32px;
		padding: 5px 12px;
		gap: 4px;
		border-radius: 999px;
		background: rgba(255,255,255,0.3);
		color: #fff;
		font-size: 24rpx;
		backdrop-filter: blur(7.5px);
		opacity: 1;
		transition: opacity 0.3s ease-in;
	}
	.search_icon{
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		// border: 1rpx solid #E5E6EB;
		display: flex;
		justify-items: center;
		justify-content: center;
		// margin-right: 25vw;
		background: rgba(255,255,255,0.3);
	}
}
/* #endif */

.index-concent-item{
	// margin-top: 10rpx;
	// position: relative;

}
.shop_content{
	padding-left: 24rpx;
	.shop_list{
		border-radius: 10rpx 10rpx 0 0;
		overflow: hidden;
		background-color: #fff;
		margin: 24rpx 0;
		margin-right: 24rpx;
		.demo-title {
			font-size: 28rpx;
			margin: 16rpx;
			color: #171B25;
		}
		.shop-price{
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10rpx 24rpx 20rpx 24rpx;
			.shop-price-num{
				display: flex;
				align-items: center;
				color: #FF0046;
				font-size: 24rpx;
				font-weight: 600;
			}
			.shop_add{
				display: flex;
			}
		}
	}
}
.gold-price{
	position: fixed;
	top: 60vh;
	right: 24rpx;
	width: 88rpx;
	height: 88rpx;
	padding: 10rpx 0;
	flex-direction: column;
	justify-content: center;
	flex-shrink: 0;
	border-radius: 8px;
	background: #FFF;
	box-shadow: 0 4px 10px 0 #0000001a;
}


.gold_new{
		padding: 40rpx 0;
		background: linear-gradient(94deg, #8F8174 -2.53%, #C4B39F 131.45%);
		view{
			color: #FFFFFF;
			text-align: center;
			font-size: 24rpx;
		}
	.gold_new_title{
		font-size: 36rpx;
		// padding-top: 40rpx;
		padding-bottom: 20rpx;
	}
}
.gold_price_show{
	display: flex;
	justify-content: space-between;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #F1F2F5;
}
	.goods_detail_footer {
		margin-top: 32rpx;
		width: 100%;
		height: 50px;
		display: flex;
		justify-content: center;
	}

	.goods_detail_footer>view {
		height: 84rpx;
		width: 80%;
		border-radius: 9999px;
		background-color: #BBA186;
		color: #FFFFFF;
		font-size: 28rpx;
		text-align: center;
		line-height: 84rpx;
	}
</style>