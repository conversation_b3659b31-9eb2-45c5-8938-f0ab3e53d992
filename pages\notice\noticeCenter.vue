<template>
  <view style="padding: 24rpx;">
  	<view class="notice-center">
  	  <view class="each-line" @tap="toNoticesList('官方公告')">
  	    <view class="label">
  	      <!-- <image class="icon" name="backImg" src="/static/img/my/icon-system-notice.png"></image> -->
  	      <text>官方公告</text>
  	    </view>
  	    <uni-icons type="right" size="16"></uni-icons>
  	  </view>
  	  <view class="each-line" @tap="toNoticesList('系统消息')">
  	    <view class="label">
  	      <!-- <image class="icon" name="backImg" src="/static/img/my/icon-system-message.png"></image> -->
  	      <text>系统消息</text>
  	    </view>
  	    <uni-icons type="right" size="16"></uni-icons>
  	  </view>
  	</view>
  </view>
</template>

<script>
export default {
  name: 'noticeCenter',
  data() {
    return {}
  },
  methods: {
    toNoticesList(type) {
			uni.navigateTo({
				url: '/pages/notice/noticesList?type=' + type
			})
		}
  }
}
</script>
<style>
	page{
		background-color: #F7F7F7;
	}
</style>
<style lang="scss" scoped>
.notice-center {
	  background-color: #fff;
	  border-radius: 10rpx;
  .each-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #F7F7F7;
    padding: 24rpx;

    .label {
    display: flex;
    align-items: center;
			.icon {
				width: 24px;
				height: 24px;
				margin-right: 10px;
			}
    }
  }
}
</style>