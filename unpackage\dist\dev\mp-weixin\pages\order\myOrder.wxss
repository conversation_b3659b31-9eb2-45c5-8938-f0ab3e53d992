@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #F7F7F7;
}
.order-list-page {
  height: 100%;
}
.order-list-page .type-tabs .segmented-control__text {
  font-size: 24rpx;
}
@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.order-list-page .order-list-header.data-v-67aadf18 {
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #FFFFFF;
}
.order-list-page .order-list-header .one-status.data-v-67aadf18 {
  padding: 10rpx 0;
  font-size: 28rpx;
  position: relative;
  color: #9FA3B0;
}
.order-list-page .order-list-header .one-status.active.data-v-67aadf18 {
  color: #171B25;
}
.order-list-page .order-list-header .one-status.active.data-v-67aadf18:after {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  background-color: #BBA186;
  position: absolute;
  bottom: 0;
  left: 0;
  transition: all 0.3s;
}
.order-list-page .order-list.data-v-67aadf18 {
  padding: 20rpx;
  overflow: auto;
}
.order-list-page .order-list .each-order.data-v-67aadf18 {
  background-color: #ffffff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}
.order-list-page .order-list .each-order .header.data-v-67aadf18 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.order-list-page .order-list .each-order .header .order-no-status.data-v-67aadf18 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.order-list-page .order-list .each-order .header .no.data-v-67aadf18 {
  display: flex;
  align-items: center;
  color: #171B25;
  font-size: 28rpx;
}
.order-list-page .order-list .each-order .header .status.data-v-67aadf18 {
  color: #FF0046;
  font-size: 28rpx;
}
.order-list-page .order-list .each-order .good-detail.data-v-67aadf18 {
  border-bottom: 1rpx solid #F7F7F7;
}
.order-list-page .order-list .each-order .good-detail .product_info.data-v-67aadf18 {
  display: flex;
  padding: 24rpx;
  background-color: #FFFFFF;
  border-radius: 10rpx;
}
.order-list-page .order-list .each-order .good-detail .product_info .prodct_left.data-v-67aadf18 {
  display: flex;
  border-radius: 10rpx;
  overflow: hidden;
}
.order-list-page .order-list .each-order .good-detail .product_info .product_center.data-v-67aadf18 {
  flex: 1;
  margin-left: 20rpx;
}
.order-list-page .order-list .each-order .good-detail .product_info .product_right.data-v-67aadf18 {
  margin-left: 32rpx;
}
.order-list-page .order-list .each-order .good-detail .product_info .product_right .font_small.data-v-67aadf18 {
  font-size: 36rpx;
  color: #171B25;
  font-weight: 600;
}
.order-list-page .order-list .each-order .opera-btns.data-v-67aadf18 {
  margin-top: 20rpx;
}
.order-list-page .order-list .each-order .opera-btns .each-btn.data-v-67aadf18 {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  border: 1px solid #ececec;
  border-radius: 200rpx;
  margin-left: 20rpx;
}
.order-list-page .order-list .each-order .opera-btns .each-btn.pay.data-v-67aadf18 {
  color: #FFFFFF;
  background-color: #BBA186;
}
