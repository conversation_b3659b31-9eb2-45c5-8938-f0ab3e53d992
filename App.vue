<style lang="scss">
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import "uview-ui/index.scss";
	.right-icon{
		width: 14rpx;
		height: 14rpx;
		border-top: 1px solid #666666;
		border-right: 1px solid #666666;
		transform: rotate(45deg);
	}
	.text-ellipsis_2{
	   overflow : hidden;
	   text-overflow: ellipsis;
	   display: -webkit-box;
	   -webkit-line-clamp: 2;
	   -webkit-box-orient: vertical;
	   word-break: break-all;
	}
	/* #ifdef WEB */
	.page_bottom {
	  height: 55px; /*暂时发现安卓小屏手机安全区为0 && 不带单位的话padding-bottom会失效，写一个兜底, 并写在最上面；f12发现css样式如果都给height设置样式，会依次往下，用最底下的一个样式，如果最下面的没失效，依次往上查找，所以兜底的写在最上面 */
	  height: calc(55px + constant(safe-area-inset-bottom)); /* 直接扩展高度，因为padding-bottom是内边距 */
	  height: calc(55px + env(safe-area-inset-bottom)); /* 直接扩展高度 */
	  padding-bottom: constant(safe-area-inset-bottom); /*兼容 iOS<11.2 */
	  padding-bottom: env(safe-area-inset-bottom); /* 兼容iOS>= 11.2*/
	}
	/* #endif */
</style>
<script>
	export default {
	onLaunch() {
	    const token = uni.getStorageSync('token');  
	    if (!token) {  
	        // 如果没有token，则跳转到登录页面  
	        // 注意：uni.reLaunch() 会关闭所有非 tabBar 页面  
	        // 如果你的登录页面是tabBar页面，可能需要使用uni.navigateTo()或其他方法  
	        // console.log('没有token，跳转到登录页面');  
	          //#ifdef APP-PLUS  
	        uni.reLaunch({  
	            url: "pages/register/register",  
	            success: () => {  
	                // 跳转完页面后再关闭启动页（仅在App平台有效）  
	                plus.navigator.closeSplashscreen();  
	            }  
	        });  
	          //#else  
	        // 在非App平台，直接使用uni.navigateTo或uni.redirectTo跳转到登录页面  
	        uni.navigateTo({  
	            url: "pages/register/register"  
	        });  
	          //#endif  
	    } else {  
	        // 如果有token，则关闭启动页（仅在App平台有效）  
	         // #ifdef APP-PLUS  
	        plus.navigator.closeSplashscreen();  
	        // 这里可以添加其他逻辑，比如跳转到首页或检查商户信息等  
	        // 例如：this.getmerDetail(); 但注意，this在onLaunch中可能不是你期望的上下文  
	          //#endif  
	    }  
	},
	onShow: function() {
		setTimeout(() => {
			// #ifdef APP-PLUS 
			plus.navigator.closeSplashscreen();
			// #endif
		}, 2000)
		// console.log('App Show')
	},
		onHide: function() {
			// console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	page{
		height: 100%;
		font-family: "黑体", SimHei, "Heiti SC", "Heiti TC", sans-serif;
	}
	
	/* 全局应用黑体字体 */
	uni-page-body,
	uni-page-refresh,
	uni-app,
	uni-page,
	view,
	scroll-view,
	swiper,
	swiper-item,
	cover-view,
	cover-image,
	text,
	rich-text,
	input,
	textarea,
	button,
	label,
	navigator,
	image {
		font-family: "黑体", SimHei, "Heiti SC", "Heiti TC", sans-serif;
	}
</style>
