<template>
    <!-- pages/UserUsage/UserUsage.wxml -->
    <view>
		<web-view :src="URL" />
    </view>
</template>

<script>
	const api = require('../../config/api');
	const util = require('../../utils/util');
export default {
    data() {
        return {
			URL:api.zhuceUrl
		};
    },
    onLoad(options) {},
    onReady() {},
    onShow() {},
    onHide() {},
    onUnload() {},
    onPullDownRefresh() {},
    onReachBottom() {},
    onShareAppMessage() {},
    methods: {}
};
</script>
<style>
</style>
