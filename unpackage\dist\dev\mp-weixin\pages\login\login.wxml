<view class="loginpage data-v-b237504c"><view style="height:50px;width:100%;" class="data-v-b237504c"></view><view class="login-img data-v-b237504c"><image style="width:150px;height:150px;border-radius:10px;" src="/static/KLLD.png" mode class="data-v-b237504c"></image></view><view class="login-form data-v-b237504c"><view class="login-account data-v-b237504c"><image style="width:15px;height:15px;" src="/static/img/login/ic_phone.png" mode="widthFix" class="data-v-b237504c"></image><input placeholder="请输入手机号" type="number" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['loginForm']]]]]}}" value="{{loginForm.phone}}" bindinput="__e" class="data-v-b237504c"/></view><view class="login-account data-v-b237504c"><image style="width:15px;height:15px;" src="/static/img/login/ic_password.png" mode="widthFix" class="data-v-b237504c"></image><block wx:if="{{passwordtype=='password'}}"><input type="password" placeholder="请输入密码" data-event-opts="{{[['input',[['__set_model',['$0','passwd','$event',[]],['loginForm']]]]]}}" value="{{loginForm.passwd}}" bindinput="__e" class="data-v-b237504c"/></block><block wx:else><input type="text" placeholder="请输入密码" data-event-opts="{{[['input',[['__set_model',['$0','passwd','$event',[]],['loginForm']]]]]}}" value="{{loginForm.passwd}}" bindinput="__e" class="data-v-b237504c"/></block><block wx:if="{{passwordtype=='password'}}"><image class="showOre data-v-b237504c" style="width:15px;height:10px;" src="/static/img/login/ic_password_normal.png" mode="widthFix" data-event-opts="{{[['tap',[['sowPasst',['$event']]]]]}}" bindtap="__e"></image></block><block wx:else><image class="showOre data-v-b237504c" style="width:15px;height:10px;" src="/static/img/login/ic_password_press.png" mode="widthFix" data-event-opts="{{[['tap',[['hideenPasst',['$event']]]]]}}" bindtap="__e"></image></block></view><view class="footer data-v-b237504c"><text data-event-opts="{{[['tap',[['gozhuce',['$event']]]]]}}" bindtap="__e" class="data-v-b237504c">立即注册</text><text data-event-opts="{{[['tap',[['goForgotPassword',['$event']]]]]}}" bindtap="__e" class="data-v-b237504c">忘记密码</text></view></view><view data-event-opts="{{[['tap',[['Login',['$event']]]]]}}" class="authorized-btn data-v-b237504c" bindtap="__e">登录</view></view>