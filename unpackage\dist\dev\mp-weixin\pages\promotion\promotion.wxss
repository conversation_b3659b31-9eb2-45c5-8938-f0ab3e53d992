
page {
	background-color: #F8F8F8;
}


@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page {
  padding: 24rpx 24rpx 0 24rpx;
}
.page .store_content {
  display: flex;
  margin-bottom: 24rpx;
  padding: 24rpx;
  border-radius: 10rpx;
  background-color: #FFFFFF;
}
.page .store_content .store_img {
  display: flex;
  border-radius: 10rpx;
  overflow: hidden;
}
.page .store_content .store_name {
  flex: 1;
  margin-left: 24rpx;
}
.store_round {
  width: 52rpx;
  height: 52rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #F5F5F5;
  display: flex;
  align-items: center;
  margin-left: 24rpx;
  justify-content: center;
}
.gold_new {
  padding: 40rpx 0;
  background: linear-gradient(94deg, #8F8174 -2.53%, #C4B39F 131.45%);
}
.gold_new view {
  color: #FFFFFF;
  text-align: center;
  font-size: 24rpx;
}
.gold_new .gold_new_title {
  font-size: 36rpx;
  padding-bottom: 20rpx;
}
.gold_price_show {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F1F2F5;
}
.goods_detail_footer {
  margin-top: 32rpx;
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: center;
}
.goods_detail_footer > view {
  height: 84rpx;
  width: 80%;
  border-radius: 9999px;
  background-color: #BBA186;
  color: #FFFFFF;
  font-size: 28rpx;
  text-align: center;
  line-height: 84rpx;
}
