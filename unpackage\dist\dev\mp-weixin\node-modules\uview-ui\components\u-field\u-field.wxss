@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-field.data-v-578c626d {
  font-size: 28rpx;
  padding: 20rpx 28rpx;
  text-align: left;
  position: relative;
  color: #303133;
}
.u-field-inner.data-v-578c626d {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-textarea-inner.data-v-578c626d {
  align-items: flex-start;
}
.u-textarea-class.data-v-578c626d {
  min-height: 96rpx;
  width: auto;
  font-size: 28rpx;
}
.fild-body.data-v-578c626d {
  display: flex;
  flex-direction: row;
  flex: 1;
  align-items: center;
}
.u-arror-right.data-v-578c626d {
  margin-left: 8rpx;
}
.u-label-text.data-v-578c626d {
  display: inline-flex;
}
.u-label-left-gap.data-v-578c626d {
  margin-left: 6rpx;
}
.u-label-postion-top.data-v-578c626d {
  flex-direction: column;
  align-items: flex-start;
}
.u-label.data-v-578c626d {
  width: 130rpx;
  flex: 1 1 130rpx;
  text-align: left;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-required.data-v-578c626d::before {
  content: "*";
  position: absolute;
  left: -16rpx;
  font-size: 14px;
  color: #fa3534;
  height: 9px;
  line-height: 1;
}
.u-field__input-wrap.data-v-578c626d {
  position: relative;
  overflow: hidden;
  font-size: 28rpx;
  height: 48rpx;
  flex: 1;
  width: auto;
}
.u-clear-icon.data-v-578c626d {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-error-message.data-v-578c626d {
  color: #fa3534;
  font-size: 26rpx;
  text-align: left;
}
.placeholder-style.data-v-578c626d {
  color: #969799;
}
.u-input-class.data-v-578c626d {
  font-size: 28rpx;
}
.u-button-wrap.data-v-578c626d {
  margin-left: 8rpx;
}
