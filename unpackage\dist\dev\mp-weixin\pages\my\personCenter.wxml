<view style="padding:24rpx;" class="data-v-1a23d4b8"><view class="pd-main first data-v-1a23d4b8"><view class="my-nav-item data-v-1a23d4b8"><text class="pd-nav-left data-v-1a23d4b8">头像</text><view data-event-opts="{{[['tap',[['picUP',['$event']]]]]}}" class="my-ni-right data-v-1a23d4b8" bindtap="__e"><image class="my-nav-img data-v-1a23d4b8" src="{{userData.imgUrl}}" mode="scaleToFill"></image><uni-icons vue-id="6ad51fe2-1" type="right" size="16" class="data-v-1a23d4b8" bind:__l="__l"></uni-icons></view></view><view data-event-opts="{{[['tap',[['onModifyName',['$event']]]]]}}" class="my-nav-item data-v-1a23d4b8" bindtap="__e"><text class="pd-nav-left data-v-1a23d4b8">姓名</text><view class="my-ni-right data-v-1a23d4b8"><text class="data-v-1a23d4b8">{{userData.userName}}</text></view></view><view class="my-nav-item data-v-1a23d4b8"><text class="pd-nav-left data-v-1a23d4b8">手机号</text><view class="my-ni-right data-v-1a23d4b8"><text class="data-v-1a23d4b8">{{userData.phone}}</text></view></view></view><u-modal vue-id="6ad51fe2-2" show-cancel-button="{{true}}" value="{{show}}" data-event-opts="{{[['^confirm',[['confirm']]],['^input',[['__set_model',['','show','$event',[]]]]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-1a23d4b8" bind:__l="__l" vue-slots="{{['default']}}"><view style="padding:24rpx;" class="data-v-1a23d4b8"><u-field bind:input="__e" vue-id="{{('6ad51fe2-3')+','+('6ad51fe2-2')}}" label="姓名" placeholder="请填写姓名" value="{{userName}}" data-event-opts="{{[['^input',[['__set_model',['','userName','$event',[]]]]]]}}" class="data-v-1a23d4b8" bind:__l="__l"></u-field></view></u-modal></view>