// 数据处理函数
function parsePrice(val) {
	if (val || val === 0) {
		const valString = val.toString();
		const decimalIndex = valString.indexOf('.');
		if (decimalIndex !== -1 && valString.length - decimalIndex === 3) {
			return valString;
		} else if (decimalIndex !== -1) {
			if (valString.length - decimalIndex === 2) {
				return valString + '0';
			} else {
				return valString.slice(0, decimalIndex + 3);
			}
		} else {
			return valString + '.00';
		}
	} else {
		return '';
	}
}
function oneparsePrice(val) {
    if (val != null && val !== '') { // 检查输入值是否存在且不为空字符串
        const valString = val.toString();
        const decimalIndex = valString.indexOf('.');
        
        if (decimalIndex !== -1) { 
            
            const decimalLength = valString.length - decimalIndex - 1;
            
            if (decimalLength === 1) { 
                return valString;
            } else if (decimalLength > 1) { 
              
                return valString.slice(0, decimalIndex + 2); 
           
                // return (Math.round(parseFloat(valString) * 10) / 10).toString();
            } else { 
                return valString + '0'; 
            }
        } else { 
            return valString + '.0'; 
        }
    } else {
        return ''; 
    }
}

function hideMiddleDigits(phoneNumber) {
	if (!phoneNumber || typeof phoneNumber !== 'string') {
		return '';
	}

	// 隐藏第4位到第8位数字
	const startVisibleIndex = 3; // 第4位的索引
	const endVisibleIndex = 6; // 第8位的索引（包括第8位）

	const visiblePart = phoneNumber.slice(0, startVisibleIndex) + '*'.repeat(endVisibleIndex - startVisibleIndex + 1);
	const hiddenPart = phoneNumber.slice(endVisibleIndex + 1);

	return `${visiblePart}${hiddenPart}`;
}
export default {
	parsePrice,
	hideMiddleDigits,
	oneparsePrice
};