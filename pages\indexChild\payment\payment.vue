<template>
	<view class="page">
		<form @submit="settleOrder">
		<view class="address">
			<view v-if="storeInfo.id">
				<view style="display: flex;justify-content: space-between;align-items: center;">
					<view class="address_top">
						<view style="display: flex;"><image src="/static/img/shop/shop_store.png" mode="widthFix" style="width: 40rpx;height: 40rpx;"></image></view>
						<view style="margin-left: 10rpx;ffont-size: 32rpx;font-weight: 600;">{{storeInfo.name}}</view>
					</view>
					<view @click="location('/pages/promotion/store')" style="display: flex; align-items: center;font-size: 24rpx;">切换门店 <view class="right-icon"></view></view>
				</view>
				<view style="margin-top: 20rpx;color: #61687C;font-size: 24rpx;">{{storeInfo.address}}</view>
			</view>
			<view @click="location('/pages/promotion/store')" class="address_right" v-else style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<image src="/static/img/shop/shop_store.png" mode="widthFix" style="width: 40rpx;height: 40rpx;">
					<text style="margin-left: 10rpx;">请选择门店</text>
				</view>
				<view class="right-icon"></view>
			</view>
		</view>
		<view class="product_info">
			<view class="prodct_left">
				<image style="width:164rpx;height: 164rpx;" :src="goodsInfo.goodsImg"></image>
			</view>
			<view class="product_center">
				<view style="font-size: 28rpx;line-height: 44rpx;" class="text-ellipsis_2">{{goodsInfo.goodsName}}</view>
				<view style="display: flex;margin-top: 20rpx;">
					<view style="color: #61687C;font-size: 24rpx;background-color: #F2F4F7;text-align: center;padding: 6rpx 20rpx;border-radius: 6rpx;">{{goodsInfo.specification}}g</view>
					<view></view>
				</view>
			</view>
			<view class="product_right">
				<view><text class="font_small">¥{{goodsInfo.price}}</text></view>
				<view style="font-size: 24rpx;color: #9FA3B0;margin-top: 8rpx;text-align: right;">x1</view>
			</view>
		</view>
		<view class="inviter" @click="onUser('/pages/indexChild/userList/userList')">
			<view>邀请人</view>
			<view style="display: flex;align-items: center;">
				<view style="display: flex;">
					<input class="uni-input" name="yqr" :value="yqrInfo.name" disabled="true" readOnly="true" placeholder="请选择邀请人" />
					</view>
				<view class="right-icon" style="margin-left: 20rpx;"></view>
			</view>
		</view>
		<view class="pay_type">
			<view>
				<view style="display: flex;"><image style="width: 56rpx;height: 56rpx;" src="/static/img/shop/shop_wx.png" alt="" /></view>
			</view>
			<view style="display: flex;justify-content: space-between;align-items: center;flex: 1;margin-left: 24rpx;">
				<view style="font-size: 28rpx;">微信</view>
				<view><image style="width: 48rpx;height: 48rpx;" src="/static/img/shop/shop_round_a.png" alt="" /></view>
			</view>
		</view>
		<view class="goods_detail_footer">
			<button class="btn" form-type="submit">确认支付</button>
		</view>
		</form>
	</view>
</template>

<script>
	import number from "../../../utils/number.js";
	const api = require('../../../config/api');
	const util = require('../../../utils/util');
	export default {
		data() {
			return {
				number: number, //声明number属性并赋值为引入的number模块
				goodsId: '',
				orderNo: '',
				ordeForm: {
					"addressId": 0,
					"goodsId": 0,
					"goodsNum": 0,
					"pickStore": "",
					"reference": "",
					"type": ""
				},
				orderPayForm: {
					"code": "",
					"img": "",
					"orderNo": '',
					"payTyle": 3
				},
				goodsInfo: {},
				storeInfo: {},
				yqrInfo: {},
				webCode: ''
			}
		},
		onLoad(o) {
			this.goodsId = o.goodsId
			this.goodsNum = parseInt(o.goodsNum)
			this.getGoodsdetail();
			this.checked = true
		},
		mounted(){
				// #ifdef WEB
				var currentUrl = window.location.href.split('?');
				let code = currentUrl[1].split('&')
				console.log(currentUrl)
				console.log(code)
				this.webCode = code[0].split('=')[1]
				// #endif
		},
		onShow() {
			let that = this
			uni.getStorage({
				key: 'store_info',
				success: function (res) {
					that.storeInfo = res.data
				}
			});
		},
		methods: {
			getUser(data){
				this.$nextTick(()=>{
					this.yqrInfo = data
				})
			},
			location(url){
				uni.navigateTo({
					url: url
				})
				this.yqrInfo = {}
			},
			onUser(url){
				if(!this.storeInfo.id){
					uni.showToast({
						title: '请选择门店',
						icon: 'none'
					})
					return
				}
				uni.navigateTo({
					url: url+'?storeId='+this.storeInfo.id
				})
			},
			async getGoodsdetail() {
				const res = await util.request(
					api.goodsDetailsUrl + '?goodsId=' + this.goodsId, {},
					'POST'
				);
				console.log(res);
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})

				} else {
					this.goodsInfo = res.data.result
				}
			},
			// 先获取订单号、传入订单号获取支付数据、传入支付数据调用微信支付
			async settleOrder(e) {
				console.log(e)
				uni.showLoading({
					title: '支付中'
				});
				let that = this
				const res = await util.request(
					api.settleOrderUrl, {
						goodsId: that.goodsId,
						goodsNum: 1,
						type: 2,
						pickStore: "",
						reference: "",
						yqr: e.detail.value.yqr,
						addressId: that.storeInfo.id
					},
					'POST'
				);
				console.log(res);
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})

				} else {
					that.orderNo = res.data
					// #ifdef MP-WEIXIN	
					uni.login({
						"provider": "weixin",
						"onlyAuthorize": true, // 微信登录仅请求授权认证
						success: async function(event){
							const {code} = event
							const result = await util.request(
								api.orderPayUrl, {
									orderNo: res.data,
									payTyle: '8',
									code: event.code
								},
								'POST'
							);
							if (result.code !== 0) {
								uni.showToast({
									title: result.msg,
									icon: "none"
								})
							
							} else {
								that.paymentRequest(result.data)
							}
						},
						fail: function (err) {
					        // 登录授权失败
					        // err.code是错误码
					    }
					})
					// #endif
					// #ifdef WEB
					const result = await util.request(
						api.orderPayUrl, {
							orderNo: res.data,
							payTyle: '7',
							code: that.webCode
						},
						'POST'
					);
					if (result.code !== 0) {
						uni.showToast({
							title: result.msg,
							icon: "none"
						})
					
					} else {
						const payWeb = JSON.parse(result.data.jspay_info)
						let param = {
							"appId": "wxa56aa346588ae42f", //公众号ID，由你传入     
							"timeStamp": payWeb.timeStamp, //时间戳，自1970年以来的秒数     
							"nonceStr": payWeb.nonceStr, //随机串     
							"package": payWeb.package,
							"signType": payWeb.signType, //微信签名方式：     
							"paySign": payWeb.paySign, //微信签名 
						}
						that.onBridgeReady(param)
					}
					// #endif
				}
			},
			// 微信公众号支付
			onBridgeReady(param) {
				let that = this;
				WeixinJSBridge.invoke('getBrandWCPayRequest', param,
					function(res) {
						if (res.err_msg == "get_brand_wcpay_request:ok") {
							console.log("微信支付成功了！！！")
							// 支付成功的回调中
							uni.showToast({
								title: '支付成功',
								icon: 'success',
								duration: 1500
							});
							//支付成功后重新回到订单详情界面并刷新
							setTimeout(() => {
								// 订单详情页面
								uni.navigateTo({
									url: '/pages/order/orderDetail?orderNo='+that.orderNo
								})
							}, 2000);
						}else{
							// uni.showToast({
							// 	title: '支付失败',
							// 	icon:'none'
							// })
							uni.hideLoading()
							setTimeout(()=>{
								// 订单详情页面
								uni.navigateTo({
									url: '/pages/order/orderDetail?orderNo='+that.orderNo
								})
							},1000)
						}
					});
			},
			// 微信小程序支付
			paymentRequest(params) {
				let that = this
				const payInfo = JSON.parse(params.jspay_info)
				uni.requestPayment({
					timeStamp: payInfo.timeStamp,
					nonceStr: payInfo.nonceStr,
					package: payInfo.package,
					signType: payInfo.signType,
					paySign: payInfo.paySign,
				    success: function (res) {
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							duration: 1500
						});
						setTimeout(()=>{
							// 订单详情页面
							uni.navigateTo({
								url: '/pages/order/orderDetail?orderNo='+that.orderNo
							})
						},1000)
				        console.log('success:' + JSON.stringify(res));
				    },
				    fail: function (err) {
						// uni.showToast({
						// 	title:'支付失败',
						// 	icon:'none'
						// })
						uni.hideLoading()
						setTimeout(()=>{
							// 订单详情页面
							uni.navigateTo({
								url: '/pages/order/orderDetail?orderNo='+that.orderNo
							})
						},1000)
				    }
				});
			},
			getCurrentDateTime() {
				const now = new Date();
				const year = now.getFullYear();
				const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
				const day = String(now.getDate()).padStart(2, '0');
				const hours = String(now.getHours()).padStart(2, '0');
				const minutes = String(now.getMinutes()).padStart(2, '0');
				const seconds = String(now.getSeconds()).padStart(2, '0');
				this.dateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
			}
		}
	}
</script>
<style>
	page{
		background-color: #F5F5F5;
	}
</style>
<style lang="scss" scoped>
	.page{
		padding: 24rpx 24rpx 0 24rpx;
	}
	.address {
		// margin-top: 24rpx;
		padding: 30rpx 24rpx;
		background-color: white;
		border-radius: 10rpx;
	}

	.address_top {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.product_info {
		margin-top: 24rpx;
		display: flex;
		padding: 24rpx;
		background-color: #FFFFFF;
		border-radius: 10rpx;
		.prodct_left {
			display: flex;
			border-radius: 10rpx;
			overflow: hidden;
		}

		.product_center {
			flex: 1;
			margin-left: 20rpx;
		}

		.product_right {
			margin-left: 32rpx;
			.font_small{
				font-size: 36rpx;
				color: #171B25;
				font-weight: 600;
			}
		}
	}
	.inviter {
		margin-top: 24rpx;
		padding: 24rpx 32rpx;
		// width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;
		border-radius: 10rpx;
		.uni-input{
			font-size: 28rpx;
			text-align: right;
		}
	}
	.pay_type{
		margin-top: 24rpx;
		display: flex;
		align-items: center;
		// justify-content: space-between;
		padding: 24rpx 32rpx;
		background-color: #fff;
		border-radius: 10rpx;
	}

	.goods_detail_footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		height: 170rpx;
		display: flex;
		justify-content: center;
		background-color: #FFFFFF;
		padding-top: 6rpx;
		.btn {
			height: 84rpx;
			width: 93%;
			border-radius: 9999px;
			background-color: #BBA186;
			color: #FFFFFF;
			font-size: 28rpx;
			text-align: center;
			line-height: 84rpx;
		}
	}
</style>