<template>
	<view class="notice-list">
		<view class="each-notice" v-for="(item, index) in noticesData" :key="index" @tap="toNoticeDetail(item)">
			<view class="left">
				<view class="notice-title">
					<view class="notice-name">{{ item.title }}</view>
					<view class="notice-content">{{ item.content }}</view>
				</view>
				<view class="notice-time">{{ item.createDate }}</view>
			</view>
			<view class="right-icon">
			</view>
		</view>
	</view>
</template>

<script>
	const api = require('../../config/api')
	const util = require('../../utils/util')
	export default {
		name: 'noticesList',
		data() {
			return {
				noticeType: '',
				noticesData: [{
						id: 20250002,
						name: '更新通知',
						time: '2023-12-12 06:45:18',
						content: '我们已经更新了一些功能，欢迎您继续使用！tabbar是原生的，层级高于前端元素，所以会遮挡住前端元素。uni-app插件市场有封装的前端tabbar，但性能不如原生tabbar'
					},
					{
						id: 20250001,
						name: '上线通知',
						time: '2023-09-01 20:32:05',
						content: '今天是我们上线的日子，我们将为您带来更好的服务，请您多多支持！如果想要一个中间带+号的tabbar，在HBuilderX中新建uni-app项目、选择 底部选项卡 模板以上大部分操作 tabbar 的 API 需要在 tabbar 渲染后才能使用，避免在 tabbar 未初始化前使用'
					}
				]
			}
		},
		onLoad(options) {
			this.noticeType = options.type
			uni.setNavigationBarTitle({
				title: this.noticeType
			})
			this.getDataList()
		},
		methods: {
			async getDataList() {
				const res = await util.request(api.noticeListUrl, {
					page: 1,
					limit: 10,
					type: this.noticeType === '官方公告' ? '0' : '1'
				}, 'POST')
				console.log(res)
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				} else {
					this.noticesData = res.data.records || []
				}
			},
			toNoticeDetail(item) {
				uni.setStorageSync('noticeData', item)
				uni.navigateTo({
					url: '/pages/notice/noticeDetail?type=' + this.noticeType + '&id=' + item.id
				})
			}
		}
	}
</script>
<style>
	page {
		background-color: #fafafa;
	}
</style>
<style lang="scss" scoped>
	.notice-list {
		.each-notice {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx;
			border-bottom: 1rpx solid #F7F7F7;
			background-color: #fff;

			.left {
				flex: 1;
				.notice-title {
					flex: 1;
					flex-grow: 1;
					display: flex;
					flex-direction: column;
					font-size: 28rpx;
					margin-bottom: 10rpx;

					.notice-name {
						color: #333;
						margin-right: 20rpx;
					}

					.notice-content {
						color: #a7a7a7;
						font-size: 24rpx;
						margin-top: 10rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}
				}

				.notice-time {
					font-size: 24rpx;
					color: #999;
					text-align: right;
				}
			}

			.right {
				width: 40rpx;
			}
		}
	}
</style>