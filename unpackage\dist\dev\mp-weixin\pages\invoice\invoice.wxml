<view class="page"><view class="form-container"><view class="order-info"><view class="order-title">订单信息</view><view class="order-item"><text class="label">订单编号：</text><text class="value">{{orderInfo.orderNo}}</text></view><view class="order-item"><text class="label">商品名称：</text><text class="value">{{orderInfo.goodsName}}</text></view><view class="order-item"><text class="label">订单金额：</text><text class="value">{{"¥"+orderInfo.totalAmt}}</text></view></view><view class="invoice-form"><view class="form-title">发票信息</view><view class="form-section"><view class="section-title">发票信息</view><view class="form-item"><view class="item-label required">发票类型</view><u-radio-group bind:input="__e" vue-id="23bf5be4-1" placement="row" value="{{invoiceForm.infoKind}}" data-event-opts="{{[['^input',[['__set_model',['$0','infoKind','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><u-radio vue-id="{{('23bf5be4-2')+','+('23bf5be4-1')}}" customStyle="{{({marginRight:'32rpx'})}}" name="12" activeColor="#BBA186" bind:__l="__l" vue-slots="{{['default']}}">普通发票</u-radio><u-radio vue-id="{{('23bf5be4-3')+','+('23bf5be4-1')}}" name="11" activeColor="#BBA186" bind:__l="__l" vue-slots="{{['default']}}">增值税专用发票</u-radio></u-radio-group></view><view class="form-item"><view class="item-label required">开票对象</view><u-radio-group bind:input="__e" vue-id="23bf5be4-4" placement="row" value="{{invoiceForm.headerType}}" data-event-opts="{{[['^input',[['__set_model',['$0','headerType','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><u-radio vue-id="{{('23bf5be4-5')+','+('23bf5be4-4')}}" customStyle="{{({marginRight:'32rpx'})}}" name="1" activeColor="#BBA186" bind:__l="__l" vue-slots="{{['default']}}">个人</u-radio><u-radio vue-id="{{('23bf5be4-6')+','+('23bf5be4-4')}}" name="0" activeColor="#BBA186" bind:__l="__l" vue-slots="{{['default']}}">企业</u-radio></u-radio-group></view><view class="form-item"><view class="item-label required">购方名称</view><u-input bind:input="__e" vue-id="23bf5be4-7" placeholder="请填写购方名称" clearable="{{true}}" value="{{invoiceForm.clientName}}" data-event-opts="{{[['^input',[['__set_model',['$0','clientName','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view><block wx:if="{{invoiceForm.headerType==='0'}}"><view class="form-item"><view class="item-label required">购方税号</view><u-input bind:input="__e" vue-id="23bf5be4-8" placeholder="请填写购方税号" clearable="{{true}}" value="{{invoiceForm.clientTaxNo}}" data-event-opts="{{[['^input',[['__set_model',['$0','clientTaxNo','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view></block><view class="form-item"><view class="item-label required">接收邮箱</view><u-input bind:input="__e" vue-id="23bf5be4-9" placeholder="请填写接收邮箱" clearable="{{true}}" value="{{invoiceForm.clientMail}}" data-event-opts="{{[['^input',[['__set_model',['$0','clientMail','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view></view></view></view><view class="footer-buttons"><button data-event-opts="{{[['tap',[['resetForm',['$event']]]]]}}" class="btn reset-btn" bindtap="__e">重置</button><button class="btn submit-btn" disabled="{{submitting}}" data-event-opts="{{[['tap',[['submitInvoice',['$event']]]]]}}" bindtap="__e">{{''+(submitting?'提交中...':'提交申请')+''}}</button></view></view>