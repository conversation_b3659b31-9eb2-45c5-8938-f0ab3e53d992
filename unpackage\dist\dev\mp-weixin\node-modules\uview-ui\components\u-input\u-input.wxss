@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-input.data-v-fdbb9fe6 {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: row;
}
.u-input__input.data-v-fdbb9fe6 {
  font-size: 28rpx;
  color: #303133;
  flex: 1;
}
.u-input__textarea.data-v-fdbb9fe6 {
  width: auto;
  font-size: 28rpx;
  color: #303133;
  padding: 10rpx 0;
  line-height: normal;
  flex: 1;
}
.u-input--border.data-v-fdbb9fe6 {
  border-radius: 6rpx;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
.u-input--error.data-v-fdbb9fe6 {
  border-color: #fa3534 !important;
}
.u-input__right-icon__item.data-v-fdbb9fe6 {
  margin-left: 10rpx;
}
.u-input__right-icon--select.data-v-fdbb9fe6 {
  transition: -webkit-transform 0.4s;
  transition: transform 0.4s;
  transition: transform 0.4s, -webkit-transform 0.4s;
}
.u-input__right-icon--select--reverse.data-v-fdbb9fe6 {
  -webkit-transform: rotate(-180deg);
          transform: rotate(-180deg);
}
