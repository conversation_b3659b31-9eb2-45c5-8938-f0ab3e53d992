{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/order/orderDetail.vue?1e6f", "webpack:///E:/pos/黄金/gold_client/pages/order/orderDetail.vue?dce2", "webpack:///E:/pos/黄金/gold_client/pages/order/orderDetail.vue?0636", "webpack:///E:/pos/黄金/gold_client/pages/order/orderDetail.vue?166d", "uni-app:///pages/order/orderDetail.vue", "webpack:///E:/pos/黄金/gold_client/pages/order/orderDetail.vue?3d22", "webpack:///E:/pos/黄金/gold_client/pages/order/orderDetail.vue?ded1", "webpack:///E:/pos/黄金/gold_client/pages/order/orderDetail.vue?608d", "webpack:///E:/pos/黄金/gold_client/pages/order/orderDetail.vue?bb95"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "addressObj", "realName", "contactPhone", "address", "orderObj", "orderNo", "orderStatus", "goodsImg", "goodsName", "price", "goodsNum", "totalAmt", "createDate", "ticketState", "downloadUrl", "storeInfo", "eyeOff", "filters", "formatState", "onLoad", "onShow", "uni", "onHide", "methods", "getOrderInfo", "title", "util", "console", "icon", "res", "settleOrder", "that", "success", "code", "api", "payTyle", "result", "fail", "paymentRequest", "timeStamp", "nonceStr", "package", "signType", "paySign", "setTimeout", "onBridgeReady", "WeixinJSBridge", "toSureReceipt", "cancelOrderEvent", "clearTimeout", "delta", "onEye", "goToInvoice", "url", "downloadInvoice", "downloadInvoiceForMiniProgram", "filePath", "fileType", "showMenu", "downloadInvoiceForH5", "window", "link", "document"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACa;AACC;;;AAGxE;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwGhrB;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;MACA;;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAD;EACA;EACAE;IACA;IACAC;MAAA;MACAH;QACAI;MACA;MACAC;QACAC;QACA;UACAN;YACAI;YACAG;UACA;QACA;UACAP;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAQ;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAT;kBACAI;gBACA;gBACAM;gBAEAV;kBACA;kBACA;kBAAA;kBACAW;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAC;8BAAA;8BAAA,OACAP,aACAQ;gCACA7B;gCACA8B;gCACAF;8BACA,GACA,OACA;4BAAA;8BAPAG;8BAQA;gCACAf;kCACAI;kCACAG;gCACA;8BAEA;gCACAG;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;kBACAM;oBACA;oBACA;kBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IA8BA;IACAC;MACA;MACA;MACAjB;QACAkB;QACAC;QACAC;QACAC;QACAC;QACAX;UACAX;YACAI;YACAG;UACA;UACAgB;YACA;YACAb;UACA;UACAJ;QACA;QACAU;UACA;UACA;UACA;UACA;UACAhB;UACAuB;YACA;YACAb;UACA;QACA;MACA;IACA;IACAc;MACA;MACAC,qDACA;QACA;UACAnB;UACA;UACAN;YACAI;YACAG;UACA;UACA;UACAgB;YACA;YACAb;UACA;QACA;UACA;UACA;UACA;UACA;UACAV;UACAuB;YACA;YACAb;UACA;QACA;MACA;IACA;IACA;IACAgB;IACA;IACAC;MACAtB;QACAC;QACA;UACAN;YACAI;YACAG;UACA;QACA;UACAP;YACAI;YACAG;UACA;UACA;YACAqB;YACA5B;cACA6B;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA/B;QACAgC;MACA;IACA;IACA;IACAC;MACA;QACAjC;UACAI;UACAG;QACA;QACA;MACA;;MAEA;;MAEA;MACA;IAOA;IAEA;IACA2B;MACAlC;QACAI;MACA;MAEAJ;QACAgC;QACArB;UACAX;UACA;YACAA;cACAmC;cACAC;cACAC;cAAA;cACA1B;gBACAL;cACA;cACAU;gBACAV;gBACAN;kBACAI;kBACAG;gBACA;cACA;YACA;UACA;YACAP;cACAI;cACAG;YACA;UACA;QACA;QACAS;UACAhB;UACAM;UACAN;YACAI;YACAG;UACA;QACA;MACA;IACA;IAEA;IACA+B;MACA;MACA;MAEA;QACA;QACAC;MACA;QACA;QACA;QACAC;QACAA;QACAA;QACAC;QACAD;QACAC;QAEAzC;UACAI;UACAG;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxbA;AAAA;AAAA;AAAA;AAAo8B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACAx9B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAmvC,CAAgB,gtCAAG,EAAC,C;;;;;;;;;;;ACAvwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/orderDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/orderDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderDetail.vue?vue&type=template&id=cbd5a456&\"\nvar renderjs\nimport script from \"./orderDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./orderDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderDetail.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./orderDetail.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/orderDetail.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=template&id=cbd5a456&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uCountDown: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-count-down/u-count-down\" */ \"uview-ui/components/u-count-down/u-count-down.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var f0 = _vm._f(\"formatState\")(_vm.orderObj.orderStatus)\n  var g0 =\n    _vm.orderObj.orderStatus == 1 && !_vm.eyeOff && _vm.orderObj.code\n      ? _vm.orderObj.code.replace(/./g, \"*\")\n      : null\n  var g1 = [\"1\", \"3\"].includes(_vm.orderObj.orderStatus)\n  var m0 = String(_vm.orderObj.orderStatus)\n  var m1 = m0 === \"3\" ? String(_vm.orderObj.ticketState) : null\n  var m2 = m0 === \"3\" ? String(_vm.orderObj.ticketState) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        f0: f0,\n        g0: g0,\n        g1: g1,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view style=\"position: relative;padding-bottom: 30rpx;\">\r\n\t\t\t<u-navbar background=\"transparent\" :back-text=\"orderObj.orderStatus | formatState\" :back-icon-size=\"30\"\r\n\t\t\t\t:back-text-style=\"{fontSize: '40rpx',marginLeft: '20rpx'}\" :border-bottom=\"false\"></u-navbar>\r\n\t\t\t<template>\r\n\t\t\t\t<view v-if=\"orderObj.orderStatus==0\"\r\n\t\t\t\t\tstyle=\"font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;\">请在\r\n\t\t\t\t\t<u-count-down :timestamp=\"orderObj.timestamp\" font-size=\"24\" bg-color=\"none\" color=\"#FF0046\"\r\n\t\t\t\t\t\tseparator=\"zh\" separator-size=\"24\" separator-color=\"#FF0046\"></u-count-down>\r\n\t\t\t\t\t前完成付款\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"orderObj.orderStatus==4\"\r\n\t\t\t\t\tstyle=\"font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;\">订单已取消，期待您再次光临</view>\r\n\t\t\t\t<view v-if=\"orderObj.orderStatus==1\"\r\n\t\t\t\t\tstyle=\"font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;\">请到店后使用</view>\r\n\t\t\t\t<view v-if=\"orderObj.orderStatus==3\"\r\n\t\t\t\t\tstyle=\"font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;\">订单已完成，期待您再次光临</view>\r\n\t\t\t\t\t<view v-if=\"orderObj.orderStatus==7\"\r\n\t\t\t\t\t\tstyle=\"font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;\">预计1-5个个工作日到账，请注意查收</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t\t<view class=\"address\">\r\n\t\t\t<view v-if=\"storeInfo.id\">\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;\">\r\n\t\t\t\t\t<view class=\"address_top\">\r\n\t\t\t\t\t\t<view style=\"display: flex;\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/shop/shop_store.png\" mode=\"widthFix\" style=\"width: 40rpx;height: 40rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"margin-left: 10rpx;ffont-size: 32rpx;font-weight: 600;\">{{storeInfo.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"display: flex; align-items: center;font-size: 24rpx;\">切换门店 <view\r\n\t\t\t\t\t\t\tclass=\"right-icon\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"margin-top: 20rpx;color: #61687C;font-size: 24rpx;\">{{storeInfo.address}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"address_right\" v-else\r\n\t\t\t\tstyle=\"width: 100%;display: flex;align-items: center;justify-content: space-between;\">\r\n\t\t\t\t<view style=\"display: flex;align-items: center;\">\r\n\t\t\t\t\t<image src=\"/static/img/shop/shop_store.png\" mode=\"widthFix\" style=\"width: 40rpx;height: 40rpx;\">\r\n\t\t\t\t\t\t<text style=\"margin-left: 10rpx;\">请选择门店</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right-icon\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"product_content\">\r\n\t\t\t<view class=\"product_info\">\r\n\t\t\t\t<view class=\"prodct_left\">\r\n\t\t\t\t\t<image style=\"width:164rpx;height: 164rpx;\" :src=\"orderObj.goodsImg\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"product_center\">\r\n\t\t\t\t\t<view style=\"font-size: 28rpx;line-height: 44rpx;\" class=\"text-ellipsis_2\">{{orderObj.goodsName}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;margin-top: 20rpx;\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tstyle=\"color: #61687C;font-size: 24rpx;background-color: #F2F4F7;text-align: center;padding: 6rpx 20rpx;border-radius: 6rpx;\">\r\n\t\t\t\t\t\t\t{{orderObj.specification}}g</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"product_right\">\r\n\t\t\t\t\t<view style=\"display: flex;justify-content: flex-end;\"><text\r\n\t\t\t\t\t\t\tclass=\"font_small\">¥{{orderObj.price}}</text></view>\r\n\t\t\t\t\t<view style=\"font-size: 24rpx;color: #9FA3B0;margin-top: 8rpx;text-align: right;\">x1</view>\r\n\t\t\t\t\t<view style=\"margin-top: 26rpx;\"><text style=\"color: #61687C;font-size: 24rpx;\">实付</text><text\r\n\t\t\t\t\t\t\tstyle=\"color: #171B25;font-size: 28rpx;margin-left: 6rpx;\">¥{{orderObj.totalAmt}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"orderObj.orderStatus==1\" class=\"product_yzm\">\r\n\t\t\t\t<view style=\"display: flex;align-items: center;\"><text>验证码</text><u-icon @click=\"onEye\"\r\n\t\t\t\t\t\t:name=\"eyeOff?'eye':'eye-off'\" :custom-style=\"{marginLeft: '10rpx'}\" size=\"36\"></u-icon></view>\r\n\t\t\t\t<view>{{eyeOff?orderObj.code:(orderObj.code ? orderObj.code.replace(/./g, \"*\") : '')}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"inviter\">\r\n\t\t\t<view>邀请人</view>\r\n\t\t\t<view style=\"display: flex;align-items: center;color: #61687C;\">{{orderObj.yqrName.name||'-'}}</view>\r\n\t\t</view>\r\n\t\t<view class=\"pay_type\">\r\n\t\t\t<view>订单信息</view>\r\n\t\t\t<view style=\"margin-top: 24rpx;\"><text style=\"color: #61687C;\">订单编号：</text><text>{{orderObj.orderNo}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"margin-top: 16rpx;\"><text\r\n\t\t\t\t\tstyle=\"color: #61687C;\">下单时间：</text><text>{{orderObj.createDate}}</text></view>\r\n\t\t\t<view v-if=\"['1','3'].includes(orderObj.orderStatus)\" style=\"margin-top: 16rpx;\"><text\r\n\t\t\t\t\tstyle=\"color: #61687C;\">付款时间：</text><text>{{orderObj.payTime}}</text></view>\r\n\t\t\t<view style=\"margin-top: 16rpx;\"><text\r\n\t\t\t\t\tstyle=\"color: #61687C;\">微信支付：</text><text>¥{{orderObj.totalAmt}}</text></view>\r\n\t\t\t<!-- 发票相关按钮 - 仅在已完成状态显示 -->\r\n\t\t\t<view v-if=\"String(orderObj.orderStatus) === '3'\" class=\"invoice_btn_container\">\r\n\t\t\t\t<!-- 未开票时显示开具发票按钮 -->\r\n\t\t\t\t<button v-if=\"String(orderObj.ticketState) === '0'\" class=\"invoice_btn\" @click=\"goToInvoice\">开具发票</button>\r\n\t\t\t\t<!-- 已开票时显示下载发票按钮 -->\r\n\t\t\t\t<button v-if=\"String(orderObj.ticketState) === '1'\" class=\"invoice_btn download_btn\" @click=\"downloadInvoice\">下载发票</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"orderObj.orderStatus==0\" class=\"goods_detail_footer\">\r\n\t\t\t<button class=\"btn\" @click=\"settleOrder\">确认支付</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst api = require('../../config/api')\r\n\tconst util = require('../../utils/util')\r\n\texport default {\r\n\t\tname: 'orderDetail',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\taddressObj: {\r\n\t\t\t\t\trealName: '',\r\n\t\t\t\t\tcontactPhone: '',\r\n\t\t\t\t\taddress: ''\r\n\t\t\t\t},\r\n\t\t\t\torderObj: {\r\n\t\t\t\t\torderNo: '',\r\n\t\t\t\t\torderStatus: '',\r\n\t\t\t\t\tgoodsImg: '',\r\n\t\t\t\t\tgoodsName: '',\r\n\t\t\t\t\tprice: '',\r\n\t\t\t\t\tgoodsNum: 0,\r\n\t\t\t\t\ttotalAmt: '',\r\n\t\t\t\t\tcreateDate: '',\r\n\t\t\t\t\tticketState: '',    // 开票状态:0-未开票、1-已开票\r\n\t\t\t\t\tdownloadUrl: ''     // 发票pdf下载地址\r\n\t\t\t\t},\r\n\t\t\t\tstoreInfo: {},\r\n\t\t\t\teyeOff: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tfilters: {\r\n\t\t\tformatState: function(_state) {\r\n\t\t\t\tif (_state === '0') {\r\n\t\t\t\t\treturn '待付款'\r\n\t\t\t\t} else if (_state === '1') {\r\n\t\t\t\t\treturn '待核销'\r\n\t\t\t\t} else if (_state === '3') {\r\n\t\t\t\t\treturn '已完成'\r\n\t\t\t\t} else if (_state === '4') {\r\n\t\t\t\t\treturn '已取消'\r\n\t\t\t\t} else if (_state === '7') {\r\n\t\t\t\t\treturn '已退款'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function(option) {\r\n\t\t\tthis.orderNo = option.orderNo\r\n\t\t\tthis.getOrderInfo()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// 监听发票申请成功事件，刷新订单数据\r\n\t\t\tuni.$on('refreshOrderDetail', () => {\r\n\t\t\t\tthis.getOrderInfo()\r\n\t\t\t})\r\n\t\t},\r\n\t\tonHide() {\r\n\t\t\t// 移除事件监听\r\n\t\t\tuni.$off('refreshOrderDetail')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取订单详情\r\n\t\t\tgetOrderInfo() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tutil.request(api.orderDetailUrl + this.orderNo, {}, 'POST').then((res) => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tlet nowTime = new Date()\r\n\t\t\t\t\t\t// let startTime = new Date(records[i].createDate);\r\n\t\t\t\t\t\tlet endTime = new Date(res.data.createDate)\r\n\t\t\t\t\t\tlet nowSeconds = (nowTime.getTime()) / 1000; // 开始时间转换为秒\r\n\t\t\t\t\t\t// let startSeconds = startTime.getTime() / 1000; // 开始时间转换为秒\r\n\t\t\t\t\t\tlet endSeconds = (endTime.getTime() + 30 * 60 * 1000) / 1000; // 结束时间转换为秒\r\n\t\t\t\t\t\tlet timestamp = endSeconds - nowSeconds; // 持续时间（秒）\r\n\t\t\t\t\t\tres.data.timestamp = timestamp\r\n\t\t\t\t\t\tthis.orderObj = res.data || {}\r\n\t\t\t\t\t\tthis.storeInfo = res.data.storeName || {}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync settleOrder() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '支付中'\r\n\t\t\t\t});\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\t\"provider\": \"weixin\",\r\n\t\t\t\t\t\"onlyAuthorize\": true, // 微信登录仅请求授权认证\r\n\t\t\t\t\tsuccess: async function(event){\r\n\t\t\t\t\t\tconst {code} = event\r\n\t\t\t\t\t\tconst result = await util.request(\r\n\t\t\t\t\t\t\tapi.orderPayUrl, {\r\n\t\t\t\t\t\t\t\torderNo: that.orderObj.orderNo,\r\n\t\t\t\t\t\t\t\tpayTyle: '8',\r\n\t\t\t\t\t\t\t\tcode: event.code\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t'POST'\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t\tif (result.code !== 0) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: result.msg,\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.paymentRequest(result.data)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function (err) {\r\n\t\t\t\t        // 登录授权失败\r\n\t\t\t\t        // err.code是错误码\r\n\t\t\t\t    }\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef WEB\r\n\t\t\t\tconst result = await util.request(\r\n\t\t\t\t\tapi.orderPayUrl, {\r\n\t\t\t\t\t\torderNo: that.orderObj.orderNo,\r\n\t\t\t\t\t\tpayTyle: '7',\r\n\t\t\t\t\t\tcode: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t);\r\n\t\t\t\tif (result.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: result.msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconst payWeb = JSON.parse(result.data.jspay_info)\r\n\t\t\t\t\tlet param = {\r\n\t\t\t\t\t\t\"appId\": \"wxa56aa346588ae42f\", //公众号ID，由你传入     \r\n\t\t\t\t\t\t\"timeStamp\": payWeb.timeStamp, //时间戳，自1970年以来的秒数     \r\n\t\t\t\t\t\t\"nonceStr\": payWeb.nonceStr, //随机串     \r\n\t\t\t\t\t\t\"package\": payWeb.package,\r\n\t\t\t\t\t\t\"signType\": payWeb.signType, //微信签名方式：     \r\n\t\t\t\t\t\t\"paySign\": payWeb.paySign, //微信签名 \r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.onBridgeReady(param)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tpaymentRequest(params) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tconst payInfo = JSON.parse(params.jspay_info)\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\ttimeStamp: payInfo.timeStamp,\r\n\t\t\t\t\tnonceStr: payInfo.nonceStr,\r\n\t\t\t\t\tpackage: payInfo.package,\r\n\t\t\t\t\tsignType: payInfo.signType,\r\n\t\t\t\t\tpaySign: payInfo.paySign,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t// 订单详情页面\r\n\t\t\t\t\t\t\tthat.getOrderInfo()\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\tconsole.log('success:' + JSON.stringify(res));\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t// \ttitle: '支付失败',\r\n\t\t\t\t\t\t// \ticon: 'none'\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t// 订单详情页面\r\n\t\t\t\t\t\t\tthat.getOrderInfo()\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonBridgeReady(param) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tWeixinJSBridge.invoke('getBrandWCPayRequest', param,\r\n\t\t\t\t\tfunction(res) {\r\n\t\t\t\t\t\tif (res.err_msg == \"get_brand_wcpay_request:ok\") {\r\n\t\t\t\t\t\t\tconsole.log(\"微信支付成功了！！！\")\r\n\t\t\t\t\t\t\t// 支付成功的回调中\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t//支付成功后重新回到订单详情界面并刷新\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t// 订单详情页面\r\n\t\t\t\t\t\t\t\tthat.getOrderInfo()\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t\t// \ttitle: res.err_msg,\r\n\t\t\t\t\t\t\t// \ticon:'none'\r\n\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\t// 订单详情页面\r\n\t\t\t\t\t\t\t\tthat.getOrderInfo()\r\n\t\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 确认收货\r\n\t\t\ttoSureReceipt() {},\r\n\t\t\t// 取消订单\r\n\t\t\tcancelOrderEvent() {\r\n\t\t\t\tutil.request(api.cancelOrderUrl + this.orderNo, {}, 'POST').then((res) => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '取消订单成功',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tlet time1 = setTimeout(() => {\r\n\t\t\t\t\t\t\tclearTimeout(time1)\r\n\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 去支付\r\n\t\t\tonEye() {\r\n\t\t\t\tthis.eyeOff = !this.eyeOff\r\n\t\t\t},\r\n\t\t\t// 跳转到开具发票页面\r\n\t\t\tgoToInvoice() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/invoice/invoice?orderNo=${this.orderObj.orderNo}&totalAmt=${this.orderObj.totalAmt}&goodsName=${encodeURIComponent(this.orderObj.goodsName)}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 下载发票\r\n\t\t\tdownloadInvoice() {\r\n\t\t\t\tif (!this.orderObj.downloadUrl) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '发票下载地址不存在',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 判断运行环境\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t// 微信小程序环境\r\n\t\t\t\tthis.downloadInvoiceForMiniProgram()\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// H5环境（包括微信公众号）\r\n\t\t\t\tthis.downloadInvoiceForH5()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\r\n\t\t\t// 微信小程序下载发票\r\n\t\t\tdownloadInvoiceForMiniProgram() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中...'\r\n\t\t\t\t})\r\n\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: this.orderObj.downloadUrl,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\t\tfileType: 'pdf',\r\n\t\t\t\t\t\t\t\tshowMenu: true,  // 显示右上角菜单按钮\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tconsole.log('PDF预览成功')\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log('PDF预览失败:', err)\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '文件预览失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '文件下载失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tconsole.log('下载失败:', err)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '网络错误，下载失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// H5环境下载发票\r\n\t\t\tdownloadInvoiceForH5() {\r\n\t\t\t\t// 检查是否在微信浏览器中\r\n\t\t\t\tconst isWechat = /micromessenger/i.test(navigator.userAgent)\r\n\r\n\t\t\t\tif (isWechat) {\r\n\t\t\t\t\t// 在微信公众号中，直接打开PDF链接\r\n\t\t\t\t\twindow.open(this.orderObj.downloadUrl, '_blank')\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 在其他浏览器中，创建下载链接\r\n\t\t\t\t\tconst link = document.createElement('a')\r\n\t\t\t\t\tlink.href = this.orderObj.downloadUrl\r\n\t\t\t\t\tlink.download = `发票_${this.orderObj.orderNo}.pdf`\r\n\t\t\t\t\tlink.target = '_blank'\r\n\t\t\t\t\tdocument.body.appendChild(link)\r\n\t\t\t\t\tlink.click()\r\n\t\t\t\t\tdocument.body.removeChild(link)\r\n\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '开始下载发票',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #F7F7F7;\r\n\t}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n\t.page {\r\n\t\tpadding: 24rpx 24rpx 0 24rpx;\r\n\t}\r\n\r\n\t.address {\r\n\t\t// margin-top: 24rpx;\r\n\t\tpadding: 30rpx 24rpx;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.address_top {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.product_content {\r\n\t\tmargin-top: 24rpx;\r\n\t\tpadding: 24rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 12rpx;\r\n\r\n\t\t.product_info {\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\r\n\t\t\t.prodct_left {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\r\n\t\t\t.product_center {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.product_right {\r\n\t\t\t\tmargin-left: 32rpx;\r\n\r\n\t\t\t\t.font_small {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tcolor: #171B25;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.product_yzm {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tborder-top: 1px dashed #F8F8F8;\r\n\t\tpadding-top: 24rpx;\r\n\t}\r\n\r\n\t.inviter {\r\n\t\tmargin-top: 24rpx;\r\n\t\tpadding: 24rpx 32rpx;\r\n\t\t// width: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\r\n\t\t.uni-input {\r\n\t\t\ttext-align: right;\r\n\t\t}\r\n\t}\r\n\r\n\t.pay_type {\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-top: 24rpx;\r\n\t\t// display: flex;\r\n\t\t// align-items: center;\r\n\t\t// justify-content: space-between;\r\n\t\tpadding: 24rpx 32rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.invoice_btn_container {\r\n\t\tmargin-top: 24rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.invoice_btn {\r\n\t\twidth: 200rpx;\r\n\t\theight: 64rpx;\r\n\t\tbackground-color: #BBA186;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 26rpx;\r\n\t\tborder-radius: 32rpx;\r\n\t\tborder: none;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t&.download_btn {\r\n\t\t\tbackground-color: #4CAF50;\r\n\t\t}\r\n\t}\r\n\r\n\t.goods_detail_footer {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 170rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding-top: 6rpx;\r\n\r\n\t\t.btn {\r\n\t\t\theight: 84rpx;\r\n\t\t\twidth: 93%;\r\n\t\t\tborder-radius: 9999px;\r\n\t\t\tbackground-color: #BBA186;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 84rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360224150\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderDetail.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360226892\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}