<view class="page data-v-57280228"><view class="status_bar data-v-57280228"><view data-event-opts="{{[['tap',[['location',['$event']]]]]}}" class="location_info data-v-57280228" bindtap="__e"><view class="data-v-57280228">{{storeInfo.name||'祥杰金条专家'}}</view><view style="display:flex;" class="data-v-57280228"><image style="width:32rpx;height:32rpx;" src="/static/img/index/index_location.png" alt class="data-v-57280228"></image></view></view><view data-event-opts="{{[['tap',[['onSearch',['$event']]]]]}}" class="search_icon data-v-57280228" bindtap="__e"><u-icon vue-id="8dd740cc-1" size="32" name="/static/img/index/search-normal.png" class="data-v-57280228" bind:__l="__l"></u-icon></view></view><view class="index-concent-item data-v-57280228"><u-swiper vue-id="8dd740cc-2" borderRadius="0" height="{{868}}" name="imgUrl" list="{{bannerlist}}" mode="none" class="data-v-57280228" bind:__l="__l"></u-swiper></view><view style="padding:30rpx 0 6rpx 0;" class="data-v-57280228"><u-divider vue-id="8dd740cc-3" half-width="260" border-color="#BBA186" bg-color="#F5F5F5" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><image style="width:128rpx;height:34rpx;" mode="widthFix" src="/static/img/index/index_tuijian.png" alt srcset class="data-v-57280228"></image></u-divider></view><view class="shop_content data-v-57280228"><u-waterfall generic:scoped-slots-left="index-u-waterfall-left" data-vue-generic="scoped" generic:scoped-slots-right="index-u-waterfall-right" bind:input="__e" vue-id="8dd740cc-4" value="{{goodsList}}" data-event-opts="{{[['^input',[['__set_model',['','goodsList','$event',[]]]]]]}}" class="data-v-57280228" bind:__l="__l" vue-slots="{{['right','left']}}"></u-waterfall></view><u-loadmore vue-id="8dd740cc-5" margin-top="{{$root.g0?'':50}}" status="{{loadStatus}}" load-text="{{loadText}}" class="data-v-57280228" bind:__l="__l"></u-loadmore><view data-event-opts="{{[['tap',[['onGoldPrice',['$event']]]]]}}" class="gold-price data-v-57280228" bindtap="__e"><view style="display:flex;justify-content:center;" class="data-v-57280228"><image style="width:36rpx;height:36rpx;" src="/static/img/index/index-gold-price.png" alt srcset class="data-v-57280228"></image></view><view style="font-size:24rpx;text-align:center;margin-top:4rpx;" class="data-v-57280228">金价</view></view><u-popup bind:input="__e" vue-id="8dd740cc-6" mode="center" border-radius="{{14}}" length="90%" close-icon-color="#FFFFFF" closeable="{{true}}" value="{{show}}" data-event-opts="{{[['^input',[['__set_model',['','show','$event',[]]]]]]}}" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view class="data-v-57280228"><view class="gold_new data-v-57280228"><view class="gold_new_title data-v-57280228">今日金价</view><view class="data-v-57280228">{{"更新于 "+timeGold}}</view></view><view style="padding:0 32rpx;" class="data-v-57280228"><view class="gold_price_show data-v-57280228"><view style="font-size:28rpx;" class="data-v-57280228">{{gyGold.remarks}}</view><view class="data-v-57280228"><text style="font-size:32rpx;color:#BBA186;" class="data-v-57280228">{{gyGold.configValue}}</text><text style="font-size:24rpx;margin-left:4rpx;" class="data-v-57280228">元/克</text></view></view><view class="gold_price_show data-v-57280228"><view style="font-size:28rpx;" class="data-v-57280228">{{tzGold.remarks}}</view><view class="data-v-57280228"><text style="font-size:32rpx;color:#BBA186;" class="data-v-57280228">{{tzGold.configValue}}</text><text style="font-size:24rpx;margin-left:4rpx;" class="data-v-57280228">元/克</text></view></view></view><view class="goods_detail_footer data-v-57280228"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-57280228">我知道了</view></view></view></u-popup></view>