<block wx:for="{{leftList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goShop',['$0'],[[['leftList','',index]]]]]]]}}" class="shop_list data-v-57280228" bindtap="__e"><image style="width:100%;height:340rpx;" src="{{item.goodsImg}}" alt class="data-v-57280228"></image><view class="demo-title text-ellipsis_2 data-v-57280228">{{''+item.goodsName+''}}</view><view class="shop-price data-v-57280228"><view class="shop-price-num data-v-57280228"><view class="data-v-57280228"><text class="data-v-57280228">¥</text><text style="font-size:32rpx;" class="data-v-57280228">{{item.price}}</text><text style="font-weight:400;color:#9FA3B0;margin-left:10rpx;" class="data-v-57280228">{{item.shop}}</text></view></view><view class="shop_add data-v-57280228"><image style="width:32rpx;height:32rpx;" src="/static/img/index/index-add-circle.png" alt srcset class="data-v-57280228"></image></view></view></view></block>