
page{
	background-color: #F9F5F2;
}

@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-57280228 {
  padding-bottom: 20rpx;
}
.status_bar.data-v-57280228 {
  position: absolute;
  z-index: 5;
  left: 0;
  top: 0;
  right: 0;
  margin-top: 98rpx;
  padding: 0 24rpx 10rpx 24rpx;
  font-size: 28rpx;
  color: #171B25;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.status_bar .location_info.data-v-57280228 {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 5px 12px;
  gap: 4px;
  border-radius: 999px;
  background: rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 24rpx;
  -webkit-backdrop-filter: blur(7.5px);
          backdrop-filter: blur(7.5px);
  opacity: 1;
  transition: opacity 0.3s ease-in;
}
.status_bar .search_icon.data-v-57280228 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  justify-items: center;
  justify-content: center;
  margin-right: 25vw;
  background: rgba(255, 255, 255, 0.3);
}
.shop_content.data-v-57280228 {
  padding-left: 24rpx;
}
.shop_content .shop_list.data-v-57280228 {
  border-radius: 10rpx 10rpx 0 0;
  overflow: hidden;
  background-color: #fff;
  margin: 24rpx 0;
  margin-right: 24rpx;
}
.shop_content .shop_list .demo-title.data-v-57280228 {
  font-size: 28rpx;
  margin: 16rpx;
  color: #171B25;
}
.shop_content .shop_list .shop-price.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 24rpx 20rpx 24rpx;
}
.shop_content .shop_list .shop-price .shop-price-num.data-v-57280228 {
  display: flex;
  align-items: center;
  color: #FF0046;
  font-size: 24rpx;
  font-weight: 600;
}
.shop_content .shop_list .shop-price .shop_add.data-v-57280228 {
  display: flex;
}
.gold-price.data-v-57280228 {
  position: fixed;
  top: 60vh;
  right: 24rpx;
  width: 88rpx;
  height: 88rpx;
  padding: 10rpx 0;
  flex-direction: column;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 8px;
  background: #FFF;
  box-shadow: 0 4px 10px 0 #0000001a;
}
.gold_new.data-v-57280228 {
  padding: 40rpx 0;
  background: linear-gradient(94deg, #8F8174 -2.53%, #C4B39F 131.45%);
}
.gold_new view.data-v-57280228 {
  color: #FFFFFF;
  text-align: center;
  font-size: 24rpx;
}
.gold_new .gold_new_title.data-v-57280228 {
  font-size: 36rpx;
  padding-bottom: 20rpx;
}
.gold_price_show.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F1F2F5;
}
.goods_detail_footer.data-v-57280228 {
  margin-top: 32rpx;
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: center;
}
.goods_detail_footer > view.data-v-57280228 {
  height: 84rpx;
  width: 80%;
  border-radius: 9999px;
  background-color: #BBA186;
  color: #FFFFFF;
  font-size: 28rpx;
  text-align: center;
  line-height: 84rpx;
}
