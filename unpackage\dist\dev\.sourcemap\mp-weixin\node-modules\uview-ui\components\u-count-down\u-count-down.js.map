{"version": 3, "sources": ["webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-count-down/u-count-down.vue?bfbf", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-count-down/u-count-down.vue?6f8e", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-count-down/u-count-down.vue?43e7", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-count-down/u-count-down.vue?efdf", "uni-app:///node_modules/uview-ui/components/u-count-down/u-count-down.vue", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-count-down/u-count-down.vue?8810", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-count-down/u-count-down.vue?bc21"], "names": ["name", "props", "timestamp", "type", "default", "autoplay", "separator", "separatorSize", "separatorColor", "color", "fontSize", "bgColor", "height", "showBorder", "borderColor", "showSeconds", "showMinutes", "showHours", "showDays", "hideZeroDay", "watch", "data", "d", "h", "i", "s", "timer", "seconds", "computed", "itemStyle", "style", "letterStyle", "mounted", "methods", "start", "formatTime", "hour", "minute", "second", "day", "showHour", "end", "clearTimer", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsDjrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA,eAwBA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;EACA;EACAgB;IACA;IACAlB;MACA;MACA;MACA;IACA;EACA;EACAmB;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;QACAC;QACAA;MACA;MACA;QACAA;QACAA;QACAA;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAR;MACA;QAAAS;QAAAC;QAAAC;MACAC;MACA;MACA;MACAH;MACA;MACA;MACA;QACAI;MACA;QACA;QACAA;MACA;MACAH;MACAC;MACA;MACAE;MACAH;MACAC;MACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAE;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACAC;QACA;MACA;IACA;EACA;EACAC;IACAD;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnRA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,yuCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-count-down/u-count-down.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-count-down.vue?vue&type=template&id=072523e7&scoped=true&\"\nvar renderjs\nimport script from \"./u-count-down.vue?vue&type=script&lang=js&\"\nexport * from \"./u-count-down.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-count-down.vue?vue&type=style&index=0&id=072523e7&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"072523e7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-count-down/u-count-down.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-count-down.vue?vue&type=template&id=072523e7&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 =\n    _vm.showDays && (_vm.hideZeroDay || (!_vm.hideZeroDay && _vm.d != \"00\"))\n      ? _vm.__get_style([_vm.itemStyle])\n      : null\n  var s1 =\n    _vm.showDays && (_vm.hideZeroDay || (!_vm.hideZeroDay && _vm.d != \"00\"))\n      ? _vm.__get_style([_vm.letterStyle])\n      : null\n  var s2 = _vm.showHours ? _vm.__get_style([_vm.itemStyle]) : null\n  var s3 = _vm.showMinutes ? _vm.__get_style([_vm.itemStyle]) : null\n  var s4 = _vm.showSeconds ? _vm.__get_style([_vm.itemStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        s4: s4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-count-down.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-count-down.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-countdown\">\r\n\t\t<view class=\"u-countdown-item\" :style=\"[itemStyle]\" v-if=\"showDays && (hideZeroDay || (!hideZeroDay && d != '00'))\">\r\n\t\t\t<view class=\"u-countdown-time\" :style=\"[letterStyle]\">\r\n\t\t\t\t{{ d }}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\tclass=\"u-countdown-colon\"\r\n\t\t\t:style=\"{fontSize: separatorSize + 'rpx', color: separatorColor, paddingBottom: separator == 'colon' ? '4rpx' : 0}\"\r\n\t\t\tv-if=\"showDays && (hideZeroDay || (!hideZeroDay && d != '00'))\"\r\n\t\t>\r\n\t\t\t{{ separator == 'colon' ? ':' : '天' }}\r\n\t\t</view>\r\n\t\t<view class=\"u-countdown-item\" :style=\"[itemStyle]\" v-if=\"showHours\">\r\n\t\t\t<view class=\"u-countdown-time\" :style=\"{ fontSize: fontSize + 'rpx', color: color}\">\r\n\t\t\t\t{{ h }}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\tclass=\"u-countdown-colon\"\r\n\t\t\t:style=\"{fontSize: separatorSize + 'rpx', color: separatorColor, paddingBottom: separator == 'colon' ? '4rpx' : 0}\"\r\n\t\t\tv-if=\"showHours\"\r\n\t\t>\r\n\t\t\t{{ separator == 'colon' ? ':' : '时' }}\r\n\t\t</view>\r\n\t\t<view class=\"u-countdown-item\" :style=\"[itemStyle]\" v-if=\"showMinutes\">\r\n\t\t\t<view class=\"u-countdown-time\" :style=\"{ fontSize: fontSize + 'rpx', color: color}\">\r\n\t\t\t\t{{ i }}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\tclass=\"u-countdown-colon\"\r\n\t\t\t:style=\"{fontSize: separatorSize + 'rpx', color: separatorColor, paddingBottom: separator == 'colon' ? '4rpx' : 0}\"\r\n\t\t\tv-if=\"showMinutes\"\r\n\t\t>\r\n\t\t\t{{ separator == 'colon' ? ':' : '分' }}\r\n\t\t</view>\r\n\t\t<view class=\"u-countdown-item\" :style=\"[itemStyle]\" v-if=\"showSeconds\">\r\n\t\t\t<view class=\"u-countdown-time\" :style=\"{ fontSize: fontSize + 'rpx', color: color}\">\r\n\t\t\t\t{{ s }}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\tclass=\"u-countdown-colon\"\r\n\t\t\t:style=\"{fontSize: separatorSize + 'rpx', color: separatorColor, paddingBottom: separator == 'colon' ? '4rpx' : 0}\"\r\n\t\t\tv-if=\"showSeconds && separator == 'zh'\"\r\n\t\t>\r\n\t\t\t秒\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * countDown 倒计时\r\n * @description 该组件一般使用于某个活动的截止时间上，通过数字的变化，给用户明确的时间感受，提示用户进行某一个行为操作。\r\n * @tutorial https://www.uviewui.com/components/countDown.html\r\n * @property {String Number} timestamp 倒计时，单位为秒\r\n * @property {Boolean} autoplay 是否自动开始倒计时，如果为false，需手动调用开始方法。见官网说明（默认true）\r\n * @property {String} separator 分隔符，colon为英文冒号，zh为中文（默认colon）\r\n * @property {String Number} separator-size 分隔符的字体大小，单位rpx（默认30）\r\n * @property {String} separator-color 分隔符的颜色（默认#303133）\r\n * @property {String Number} font-size 倒计时字体大小，单位rpx（默认30）\r\n * @property {Boolean} show-border 是否显示倒计时数字的边框（默认false）\r\n * @property {Boolean} hide-zero-day 当\"天\"的部分为0时，隐藏该字段 （默认true）\r\n * @property {String} border-color 数字边框的颜色（默认#303133）\r\n * @property {String} bg-color 倒计时数字的背景颜色（默认#ffffff）\r\n * @property {String} color 倒计时数字的颜色（默认#303133）\r\n * @property {String} height 数字高度值(宽度等同此值)，设置边框时看情况是否需要设置此值，单位rpx（默认auto）\r\n * @property {Boolean} show-days 是否显示倒计时的\"天\"部分（默认true）\r\n * @property {Boolean} show-hours 是否显示倒计时的\"时\"部分（默认true）\r\n * @property {Boolean} show-minutes 是否显示倒计时的\"分\"部分（默认true）\r\n * @property {Boolean} show-seconds 是否显示倒计时的\"秒\"部分（默认true）\r\n * @event {Function} end 倒计时结束\r\n * @event {Function} change 每秒触发一次，回调为当前剩余的倒计秒数\r\n * @example <u-count-down ref=\"uCountDown\" :timestamp=\"86400\" :autoplay=\"false\"></u-count-down>\r\n */\r\nexport default {\r\n\tname: 'u-count-down',\r\n\tprops: {\r\n\t\t// 倒计时的时间，秒为单位\r\n\t\ttimestamp: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 是否自动开始倒计时\r\n\t\tautoplay: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 用英文冒号(colon)或者中文(zh)当做分隔符，false的时候为中文，如：\"11:22\"或\"11时22秒\"\r\n\t\tseparator: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'colon'\r\n\t\t},\r\n\t\t// 分隔符的大小，单位rpx\r\n\t\tseparatorSize: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 30\r\n\t\t},\r\n\t\t// 分隔符颜色\r\n\t\tseparatorColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"#303133\"\r\n\t\t},\r\n\t\t// 字体颜色\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#303133'\r\n\t\t},\r\n\t\t// 字体大小，单位rpx\r\n\t\tfontSize: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 30\r\n\t\t},\r\n\t\t// 背景颜色\r\n\t\tbgColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#fff'\r\n\t\t},\r\n\t\t// 数字框高度，单位rpx\r\n\t\theight: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 'auto'\r\n\t\t},\r\n\t\t// 是否显示数字框\r\n\t\tshowBorder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 边框颜色\r\n\t\tborderColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#303133'\r\n\t\t},\r\n\t\t// 是否显示秒\r\n\t\tshowSeconds: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 是否显示分钟\r\n\t\tshowMinutes: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 是否显示小时\r\n\t\tshowHours: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 是否显示“天”\r\n\t\tshowDays: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 当\"天\"的部分为0时，不显示\r\n\t\thideZeroDay: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t// 监听时间戳的变化\r\n\t\ttimestamp(newVal, oldVal) {\r\n\t\t\t// 如果倒计时间发生变化，清除定时器，重新开始倒计时\r\n\t\t\tthis.clearTimer();\r\n\t\t\tthis.start();\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\td: '00', // 天的默认值\r\n\t\t\th: '00', // 小时的默认值\r\n\t\t\ti: '00', // 分钟的默认值\r\n\t\t\ts: '00', // 秒的默认值\r\n\t\t\ttimer: null ,// 定时器\r\n\t\t\tseconds: 0, // 记录不停倒计过程中变化的秒数\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\t// 倒计时item的样式，item为分别的时分秒部分的数字\r\n\t\titemStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\tif(this.height) {\r\n\t\t\t\tstyle.height = this.height + 'rpx';\r\n\t\t\t\tstyle.width = this.height + 'rpx';\r\n\t\t\t}\r\n\t\t\tif(this.showBorder) {\r\n\t\t\t\tstyle.borderStyle = 'solid';\r\n\t\t\t\tstyle.borderColor = this.borderColor;\r\n\t\t\t\tstyle.borderWidth = '1px';\r\n\t\t\t}\r\n\t\t\tif(this.bgColor) {\r\n\t\t\t\tstyle.backgroundColor = this.bgColor;\r\n\t\t\t}\r\n\t\t\treturn style;\r\n\t\t},\r\n\t\t// 倒计时数字的样式\r\n\t\tletterStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\tif(this.fontSize) style.fontSize = this.fontSize +  'rpx';\r\n\t\t\tif(this.color) style.color = this.color;\r\n\t\t\treturn style;\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\t// 如果自动倒计时\r\n\t\tthis.autoplay && this.timestamp && this.start();\r\n\t},\r\n\tmethods: {\r\n\t\t// 倒计时\r\n\t\tstart() {\r\n\t\t\t// 避免可能出现的倒计时重叠情况\r\n\t\t\tthis.clearTimer();\r\n\t\t\tif (this.timestamp <= 0) return;\r\n\t\t\tthis.seconds = Number(this.timestamp);\r\n\t\t\tthis.formatTime(this.seconds);\r\n\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\tthis.seconds--;\r\n\t\t\t\t// 发出change事件\r\n\t\t\t\tthis.$emit('change', this.seconds);\r\n\t\t\t\tif (this.seconds < 0) {\r\n\t\t\t\t\treturn this.end();\r\n\t\t\t\t}\r\n\t\t\t\tthis.formatTime(this.seconds);\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\t\t// 格式化时间\r\n\t\tformatTime(seconds) {\r\n\t\t\t// 小于等于0的话，结束倒计时\r\n\t\t\tseconds <= 0 && this.end();\r\n\t\t\tlet [day, hour, minute, second] = [0, 0, 0, 0];\r\n\t\t\tday = Math.floor(seconds / (60 * 60 * 24));\r\n\t\t\t// 判断是否显示“天”参数，如果不显示，将天部分的值，加入到小时中\r\n\t\t\t// hour为给后面计算秒和分等用的(基于显示天的前提下计算)\r\n\t\t\thour = Math.floor(seconds / (60 * 60)) - day * 24;\r\n\t\t\t// showHour为需要显示的小时\r\n\t\t\tlet showHour = null;\r\n\t\t\tif(this.showDays) {\r\n\t\t\t\tshowHour = hour;\r\n\t\t\t} else {\r\n\t\t\t\t// 如果不显示天数，将“天”部分的时间折算到小时中去\r\n\t\t\t\tshowHour = Math.floor(seconds / (60 * 60));\r\n\t\t\t}\r\n\t\t\tminute = Math.floor(seconds / 60) - hour * 60 - day * 24 * 60;\r\n\t\t\tsecond = Math.floor(seconds) - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60;\r\n\t\t\t// 如果小于10，在前面补上一个\"0\"\r\n\t\t\tshowHour = showHour < 10 ? '0' + showHour : showHour;\r\n\t\t\tminute = minute < 10 ? '0' + minute : minute;\r\n\t\t\tsecond = second < 10 ? '0' + second : second;\r\n\t\t\tday = day < 10 ? '0' + day : day;\r\n\t\t\tthis.d = day;\r\n\t\t\tthis.h = showHour;\r\n\t\t\tthis.i = minute;\r\n\t\t\tthis.s = second;\r\n\t\t},\r\n\t\t// 停止倒计时\r\n\t\tend() {\r\n\t\t\tthis.clearTimer();\r\n\t\t\tthis.$emit('end', {});\r\n\t\t},\r\n\t\t// 清除定时器\r\n\t\tclearTimer() {\r\n\t\t\tif(this.timer) {\r\n\t\t\t\t// 清除定时器\r\n\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\tthis.timer = null;\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\tclearInterval(this.timer);\r\n\t\tthis.timer = null;\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\r\n\t.u-countdown {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-flex;\t\t\r\n\t\t/* #endif */\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.u-countdown-item {\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 2rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t\twhite-space: nowrap;\r\n\t\ttransform: translateZ(0);\r\n\t}\r\n\r\n\t.u-countdown-time {\r\n\t\tmargin: 0;\r\n\t\tpadding: 0;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t.u-countdown-colon {\r\n\t\t@include vue-flex;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 0 5rpx;\r\n\t\tline-height: 1;\r\n\t\talign-items: center;\r\n\t\tpadding-bottom: 4rpx;\r\n\t}\r\n\r\n\t.u-countdown-scale {\r\n\t\ttransform: scale(0.9);\r\n\t\ttransform-origin: center center;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-count-down.vue?vue&type=style&index=0&id=072523e7&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-count-down.vue?vue&type=style&index=0&id=072523e7&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360227001\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}