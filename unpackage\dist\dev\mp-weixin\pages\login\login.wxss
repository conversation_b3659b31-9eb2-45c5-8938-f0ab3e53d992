@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.loginpage.data-v-b237504c {
  padding: 2px;
}
.login-img.data-v-b237504c {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
}
/* 表单部分 */
.login-form.data-v-b237504c {
  margin-left: 25px;
  margin-right: 25px;
  margin-top: 25px;
}
.login-account.data-v-b237504c {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 13px;
  align-items: center;
  background-color: #fff;
  height: 45px;
  border-bottom: 1px solid #E5E5E5;
}
.login-account input.data-v-b237504c {
  flex: 1;
  margin-left: 15px;
  padding: 5px;
  padding-right: 15px;
}
.inp-palcehoder.data-v-b237504c {
  font-size: 13px;
}
.input-item.data-v-b237504c {
  position: relative;
  margin-left: 20px;
  width: 40px;
}
.footer.data-v-b237504c {
  font-size: 14px;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.authorized-btn.data-v-b237504c {
  margin-top: 15px;
  margin-left: 15px;
  margin-right: 15px;
  height: 44px;
  line-height: 44px;
  color: #fff;
  text-align: center;
  background-color: #6A9FFB;
  border-radius: 20px;
}
.footer.data-v-b237504c {
  color: #76A7FB;
  font-size: 15px;
}
