@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #F7F7F7;
}
.page {
  min-height: 100vh;
}
.form-container {
  padding: 24rpx;
}
.order-info {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.order-info .order-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #171B25;
  margin-bottom: 20rpx;
}
.order-info .order-item {
  display: flex;
  margin-bottom: 12rpx;
}
.order-info .order-item .label {
  color: #61687C;
  font-size: 28rpx;
  width: 160rpx;
}
.order-info .order-item .value {
  color: #171B25;
  font-size: 28rpx;
  flex: 1;
}
.invoice-form {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx;
}
.invoice-form .form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #171B25;
  margin-bottom: 24rpx;
}
.invoice-form .form-section {
  margin-bottom: 32rpx;
}
.invoice-form .form-section .section-title {
  font-size: 28rpx;
  color: #171B25;
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  border-bottom: 1px solid #F2F4F7;
}
.invoice-form .form-item {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}
.invoice-form .form-item .item-label {
  font-size: 28rpx;
  color: #171B25;
  width: 160rpx;
  flex-shrink: 0;
}
.invoice-form .form-item .item-label.required::after {
  content: "*";
  color: #FF0046;
  margin-left: 4rpx;
}
.footer-buttons {
  background-color: #FFFFFF;
  padding: 24rpx;
  display: flex;
  gap: 24rpx;
  margin-top: 24rpx;
}
.footer-buttons .btn {
  flex: 1;
  height: 84rpx;
  border-radius: 42rpx;
  font-size: 28rpx;
  border: none;
  line-height: 84rpx;
}
.footer-buttons .btn.reset-btn {
  background-color: #F2F4F7;
  color: #61687C;
}
.footer-buttons .btn.submit-btn {
  background-color: #BBA186;
  color: #FFFFFF;
}
.footer-buttons .btn.submit-btn:disabled {
  background-color: #D0D0D0;
  color: #999999;
}
