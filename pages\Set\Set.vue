<template>
	<view class="">
		<view class="set-item" @click="upadetaApp">
			<view class="">
				检查更新
			</view>
			<view class="set-item-img">
				<text>{{curVersion}}</text>
				<view class="right-icon"></view>
				<!-- <image src="../../static/img/index/ic_next_black.png" mode="widthFix"></image> -->
			</view>
		</view>
		<view class="">
			<button type="default" class="button" @click="logOut">退出登录</button>
		</view>
	</view>
</template>

<script>
	const api = require('../../config/api');
	const util = require('../../utils/util');
	import number from "../../utils/number.js";
	export default {
		data() {
			return {
				curVersion: '',
				curVersionCode: '',
				number: number, //声明number属性并赋值为引入的number模块
				userInfo: {},
				totalAmt: 0,
				balance: 0,
				phone: '18205405955',
				changeInform: {
					"isVoiceBroadcast": "",
					"nickName": "",
					"userImg": ""
				}
			}
		},
		onShow() {
			// this.upadetaApp();
			//#ifdef APP-PLUS
			this.getApp();
			//#endif
		},
		onLoad() {
			// this.getuserInfo();
		},
		methods: {
			toupdataPassd(){
				uni.navigateTo({
					url: '/pages/ForgotPassword/ForgotPassword'
				});
			},
			
			// 版本更新
			getApp() {
				plus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {
					this.curVersion = wgtinfo.version; //应用版本名称
					this.curVersionCode = wgtinfo.versionCode; //应用版本号
				});
			},
			// 版本更新
			upadetaApp() {
				uni.showLoading({
					title: '正在检查更新'
				})
				setTimeout(()=>{
					uni.showToast({
						title: '已是最新版本',
						icon: 'none'
					});
				},1000)
			},
			logOut() {
				uni.removeStorageSync("token");
				uni.removeStorageSync("store_info");
				uni.reLaunch({
					url: '/pages/register/register'
				});
			},
		}

	}
</script>

<style>
	.set-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;
		border-bottom: 0.5px solid #F4F6FA;
		position: relative;
		padding: 15px;
	}

	.set-item-img {
		display: flex;
		align-items: center;
	}

	.set-item-img image {
		width: 10px;
		height: 20px;
		margin-left: 10px;
	}

/* 	.button {
		width: 210px;
		height: 40px;
		background-color: #2C5FE5 !important;
		color: #fff !important;
		line-height: 40px;
		margin-top: 30px;
		border-radius: 20px;
		font-size: 14px;
	} */
	.button {
		
		width: 225px;
		height: 44px;
		margin: 10px auto;
		text-align: center;
		/* padding-right: 15px; */
		color: #fff !important;
		font-size: 14px;
		line-height: 44px;
		background-color: #BBA186 !important;
		border-radius: 5px;
		margin-top: 20px;
	}
</style>