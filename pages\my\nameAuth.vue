<template>
	<view class="real-auth-page">
		<view class="each-line">
			<text class="label">姓名</text>
			<input class="no-input" :disabled="true" v-model="authInfo.realName" type="text" placeholder="请输入姓名" />
		</view>
		<view class="each-line">
			<text class="label">身份证号</text>
			<input class="no-input" :disabled="true" v-model="authInfo.idCard" type="text" placeholder="请输入身份证号" />
		</view>
		<view class="each-line">
			<text class="label">手机号</text>
			<input class="no-input" :disabled="true" v-model="authInfo.phone" type="text" placeholder="请输入手机号" />
		</view>
		<!-- <button class="submit-btn" type="default" @tap="submitEvent">提交</button> -->
	</view>
</template>

<script>
	const api = require('../../config/api')
	const util = require('../../utils/util')
	export default {
		name: 'realAuth',
		data() {
			return {
				authInfo: {
					realName: '',
					idCard: '',
					phone: ''
				}
			}
		},
		onLoad: function(options) {
			this.getUserInfo()
		},
		methods: {
			async getUserInfo() {
				const res = await util.request(api.getUserInfoUrl, {}, 'POST')
				console.log(res)
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				} else {
					if (res.data.realName) {
						this.authInfo.realName = util.processName(res.data.realName.realName) 
						this.authInfo.idCard = util.phoneSubstringfct(res.data.realName.idCard) 
						this.authInfo.phone = util.phoneSubstring(res.data.realName.phone) 
					} else {
							uni.navigateTo({
								url: '/pages/my/myBankCard?from=nameAuth'
							})
					}
				}
			},
			submitEvent() {
				// util.request(api.realNameUrl, this.authInfo, 'POST').then(res => {
				// 	console.log(res)
				// 	if (res.code !== 0) {
				// 		debugger
				// 		uni.showToast({
				// 			title: res.msg,
				// 			icon: 'none'
				// 		})
				// 	} else {
				// 		debugger
				// 		if (res.data.realName) {
				// 			this.authInfo.realName = res.data.realName
				// 			this.authInfo.idCard = res.data.realName
				// 		}
				// 	}
				// })
			}
		}
	}
</script>

<style lang="scss" scoped>
	.real-auth-page {
		.each-line {
			display: flex;
			justify-content: normal;
			border-bottom: 1px solid #e5e5e5;
			padding: 20rpx 40rpx;

			.label {
				width: 160rpx;
			}

			.no-input {
				padding-left: 30rpx;
				flex-grow: 1;
				font-size: 28rpx;
			}
		}

		.idcard-image {
			display: flex;
			justify-content: space-around;
			margin-top: 120rpx;

			.imgUpload {
				width: 360rpx;
				align-items: center;
				justify-content: center;
				text-align: center;

				image {
					border: 1rpx solid #b6b4b4;
					width: 340rpx;
					height: 228rpx;
				}
			}
		}

		.submit-btn {
			width: 90%;
			height: 90rpx;
			line-height: 90rpx;
			color: #fff;
			font-size: 32rpx;
			margin-top: 40rpx;
			border: 1px solid #007aff;
			background-color: #007aff;
			border-radius: 20rpx;
		}
	}
</style>