<template>
	<view class="container">
		<view class="mh-person">
			<view v-if="loginState" style="display: flex;border-radius: 50%;overflow: hidden;"><image class="mh-p-img" :src="userData.imgUrl"></image></view>
			<view v-else class="mh-p-img mh-p-img-bg">
				<text class="iconfont icon-wode"></text>
			</view>
			<view v-if="loginState" class="mh-p-center">
				<view class="mh-pc-text1">{{ userData.userName }}</view>
				<view class="mh-pc-text2">{{ userData.phone }}</view>
			</view>
			<view v-else class="mh-p-center" @tap="login">登录</view>
		</view>
		<view class="my_order">
			<view class="my_order_title">
				<view style="font-size: 28rpx;font-weight: 600;">我的订单</view>
				<view style="display: flex;align-items: center;" @click="goOrder(5)">
					<view style="color: #9FA3B0;font-size: 24rpx;">全部订单</view>
					<view class="right-icon" style="margin-left: 6rpx;"></view>
				</view>
			</view>
			<view class="my_order_state">
				<view class="my_order_list" @click="goOrder(0)">
					<view style="display: flex;justify-content: center;"><image style="width: 48rpx;height: 48rpx;" src="/static/img/my/my_wallet-2.png" alt="" /></view>
					<view style="font-size: 24rpx;text-align: center;margin-top: 12rpx;">待付款</view>
				</view>
				<view class="my_order_list" @click="goOrder(1)">
					<view style="display: flex;justify-content: center;"><image style="width: 48rpx;height: 48rpx;" src="/static/img/my/my_transaction-minus.png" alt="" /></view>
					<view style="font-size: 24rpx;text-align: center;margin-top: 12rpx;">待核销</view>
				</view>
				<view class="my_order_list" @click="goOrder(3)">
					<view style="display: flex;justify-content: center;"><image style="width: 48rpx;height: 48rpx;" src="/static/img/my/my_receipt-item.png" alt="" /></view>
					<view style="font-size: 24rpx;text-align: center;margin-top: 12rpx;">已完成</view>
				</view>
				<view class="my_order_list" @click="goOrder(5)">
					<view style="display: flex;justify-content: center;"><image style="width: 48rpx;height: 48rpx;" src="/static/img/my/my_receipt-search.png" alt="" /></view>
					<view style="font-size: 24rpx;text-align: center;margin-top: 12rpx;">全部订单</view>
				</view>
			</view>
		</view>
		<view class="my-nav-content">
			<view class="my-nav-item" v-for="(item, index) in subMenu" :key="index" @tap="toUrl(item)">
				<view style="display: flex;"><image class="icon" :src="item.icon" alt="" /></view>
				<view class="my-ni-left">
					<view>{{item.name}}</view>
					<view class="right-icon"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// pages/my/my.js
	const api = require('../../config/api')
	const util = require('../../utils/util')
	const app = getApp()
	export default {
		name: 'my',
		data() {
			return {
				isTest: null,
				pageLoad: true,
				loginState: false,
				userData: {},
				show: false,
				uid: '1',
				token: '123321123',
				phone: '',
				subMenu: [{
						name: '个人中心',
						icon: '/static/img/my/my_use.png',
						url: '/pages/my/personCenter'
					},
					{
						name: '消息中心',
						icon: '/static/img/my/my_message.png',
						url: '/pages/notice/noticesList?type=官方公告'
					},
					{
						name: '提货点',
						icon: '/static/img/shop/shop_store.png',
						url: '/pages/promotion/store?type=1'
					},
					{
						name: '售后服务',
						icon: '/static/img/my/my_star.png',
						url: ''
					},
					{
						name: '设置',
						icon: '/static/img/my/my_setting.png',
						url: '/pages/Set/Set'
					}
				]
			}
		},
		onLoad(options) {
			// this.getUserInfo()
		},
		onShow() {
			this.isTest = uni.getStorageSync('isTest')
			this.getUserInfo()
			this.getUserInfokefu()
		},
		methods: {
			goOrder(status) {
				uni.setStorage({
					key: 'roder_status',
					data: status,
					success: function () {
						setTimeout(()=>{
							uni.switchTab({
								url: "/pages/order/myOrder"
							})
						},600)
					}
				});
			},
			tmyOrder() {
				uni.navigateTo({
					url: '/pages/order/myOrder'
				});
			},
			logOut() {
				uni.removeStorageSync("token");
				uni.reLaunch({
					url: '/pages/register/register'
				})

			},
			tomyjifen() {
				uni.navigateTo({
					url: '/pages/indexChild/Redeem/Redeem'
				})
			},
			tomyjifen() {
				uni.navigateTo({
					url: '/pages/indexChild/Redeem/Redeem'
				})
			},
			async getUserInfo() {
				const res = await util.request(api.getUserInfoUrl, {}, 'POST')
				// console.log(res)
				if (res.code !== 0) {
					this.loginState = false
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				} else {
					this.loginState = true
					this.userData = res.data.user
					// console.log(res.data.user)
				}
			},
			login() {},
			toUrl(item) {
				if (item.name == '售后服务'&&this.phone) {
					uni.makePhoneCall({
						phoneNumber: this.phone //仅为示例
					});
				} else {
					uni.navigateTo({
						url: item.url
					})
				}
			},
			async getUserInfokefu() {
				let that = this
				const res = await util.request(api.getUserkefuUrl, {}, 'POST')
				console.log(res)
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				} else {
					that.phone = res.data.configValue
				}
			},
		}
	}
</script>
<style>
	page {
		background-color: #F7F7F7;
	}
</style>
<style lang="scss" scoped>
	.container {
		padding: 24rpx;
	}

	.mh-person {
		display: flex;
		align-items: center;
		.mh-p-img {
			width: 100rpx;
			height: 100rpx;
			&.mh-p-img-bg {
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #fff;
				.iconfont {
					font-size: 2.25rem;
					color: #999999;
				}
			}
		}
		.mh-p-center {
			margin-left: 20rpx;
			.mh-pc-text1 {
				font-size: 36rpx;
				font-weight: 600;
				color: #171B25;
			}

			.mh-pc-text2 {
				margin-top: 10rpx;
				font-size: 24rpx;
				color: #61687C;
			}
		}
	}
	.my_order{
		margin-top: 24rpx;
		background-color: white;
		border-radius: 10rpx;
		padding: 30rpx 0;
		.my_order_title{
			padding: 0 32rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.my_order_state{
			display: flex;
			margin-top: 20rpx;
			.my_order_list{
				padding-top: 10rpx;
				width: 25%;
			}
		}
	}
	.my-nav-content{
		margin-top: 24rpx;
		background-color: white;
		border-radius: 14rpx;
		.my-nav-item {
			display: flex;
			align-items: center;
			padding-left: 24rpx;
			.my-ni-left {
				flex: 1;
				margin-left: 20rpx;
				padding: 30rpx 30rpx 30rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid #F7F7F7;
			}
			.icon {
				width: 32rpx;
				height: 32rpx;
			}
		}
	}

</style>