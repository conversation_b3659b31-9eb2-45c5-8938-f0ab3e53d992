{"_from": "uview-ui@1.8.8", "_id": "uview-ui@1.8.8", "_inBundle": false, "_integrity": "sha512-Osal3yzXiHor0In9OPTZuXTaqTbDglMZ9RGK/MPYDoQQs+y0hrBCUD0Xp5T70C8i2lLu2X6Z11zJhmsQWMR7Jg==", "_location": "/uview-ui", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "uview-ui@1.8.8", "name": "uview-ui", "escapedName": "uview-ui", "rawSpec": "1.8.8", "saveSpec": null, "fetchSpec": "1.8.8"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/uview-ui/-/uview-ui-1.8.8.tgz", "_shasum": "2db41f5b5d59452eb2a17e4aa0b684a004ed0265", "_spec": "uview-ui@1.8.8", "_where": "D:\\msf\\pos2_hhlm", "author": {"name": "uView"}, "bundleDependencies": false, "deprecated": false, "description": "uView UI，是uni-app生态优秀的UI框架，全面的组件和便捷的工具会让您信手拈来，如鱼得水", "devDependencies": {"sass": "1.26.2", "sass-loader": "8.0.2"}, "keywords": ["uview", "uView", "uni-app", "uni-app ui", "uniapp", "uviewui", "uview ui", "uviewUI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uViewUI", "uView UI", "uni ui", "uni UI", "uniapp ui", "ui", "UI框架", "uniapp ui框架", "uniapp UI"], "license": "MIT", "main": "index.js", "name": "uview-ui", "repository": {"type": "git", "url": ""}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.8.8"}