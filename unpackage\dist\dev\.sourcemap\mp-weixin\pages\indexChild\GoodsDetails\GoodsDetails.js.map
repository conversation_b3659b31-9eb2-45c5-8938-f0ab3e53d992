{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/GoodsDetails/GoodsDetails.vue?31c8", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/GoodsDetails/GoodsDetails.vue?a7da", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/GoodsDetails/GoodsDetails.vue?7310", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/GoodsDetails/GoodsDetails.vue?23ba", "uni-app:///pages/indexChild/GoodsDetails/GoodsDetails.vue", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/GoodsDetails/GoodsDetails.vue?bab6", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/GoodsDetails/GoodsDetails.vue?8ca3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "number", "goodsId", "Goodsitem", "show", "goodsNum", "onLoad", "onShow", "methods", "goPay", "uni", "url", "showPopup", "getGoodsdetail", "title", "util", "api", "res", "console", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6DjrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EAAA,CACA;EACAC;IACAC;MACAC;QACAC,4FACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;kBACAI;gBACA;gBAAA;gBAAA,OACAC,aACAC,wCACA,OACA;cAAA;gBAHAC;gBAIAC;gBACA;kBACAR;oBACAI;oBACAK;kBACA;gBAEA;kBACAT;kBACAO;kBACAA;kBACAA;kBACAA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtHA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,yuCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/indexChild/GoodsDetails/GoodsDetails.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/indexChild/GoodsDetails/GoodsDetails.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./GoodsDetails.vue?vue&type=template&id=5d37e7c3&scoped=true&\"\nvar renderjs\nimport script from \"./GoodsDetails.vue?vue&type=script&lang=js&\"\nexport * from \"./GoodsDetails.vue?vue&type=script&lang=js&\"\nimport style0 from \"./GoodsDetails.vue?vue&type=style&index=0&id=5d37e7c3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5d37e7c3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/indexChild/GoodsDetails/GoodsDetails.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./GoodsDetails.vue?vue&type=template&id=5d37e7c3&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uDivider: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-divider/u-divider\" */ \"uview-ui/components/u-divider/u-divider.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./GoodsDetails.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./GoodsDetails.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<view style=\"position: absolute;left: 0;top: 0;right: 0;z-index: 5;\">\r\n\t\t\t<u-navbar background=\"transparent\" :back-icon-color=\"'#FFFFFF'\" :border-bottom=\"false\"></u-navbar>\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<view class=\"img\">\r\n\t\t\t<u-swiper :height=\"750\" borderRadius=\"0\" imgMode=\"widthFix\" name=\"imgUrl\" :list=\"Goodsitem.goodsImg\" mode=\"none\"></u-swiper>\r\n\t\t</view>\r\n\t\t<view class=\"goods_content\">\r\n\t\t\t<view class=\"goods_padding\">\r\n\t\t\t\t<view class=\"goods_price\">\r\n\t\t\t\t\t<text><text style=\"font-size: 32rpx;\">¥</text>{{Goodsitem.price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tips_item\"  v-for=\"(item,index) in Goodsitem.goodsDescribe\" :key=\"index\">{{item}}</view>\r\n\t\t\t\t<view class=\"goods_name\">{{Goodsitem.goodsName}}</view>\r\n\t\t\t\t<view class=\"goods_desc\">\r\n\t\t\t\t\t<view>已成交 {{Goodsitem.sales}} 件</view>\r\n\t\t\t\t\t<view>剩余 {{Goodsitem.inventory}} 件</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"goods_params\">\r\n\t\t\t\t<view class=\"goods_specifica\">\r\n\t\t\t\t\t<view style=\"display: flex;\">\r\n\t\t\t\t\t\t<view style=\"color: #61687C;\">保障</view>\r\n\t\t\t\t\t\t<view style=\"margin-left: 24rpx;display: flex;color: #171B25;\">\r\n\t\t\t\t\t\t\t<view style=\"display: flex;align-items: center;margin-right: 32rpx;\" v-for=\"item in Goodsitem.baozhang\">\r\n\t\t\t\t\t\t\t\t<view style=\"display: flex;\"><image style=\"width: 25rpx;height: 25rpx;\" src=\"/static/img/shop/shop_tick-circle.png\" alt=\"\" srcset=\"\" /></view>\r\n\t\t\t\t\t\t\t\t<view style=\"margin-left: 8rpx;\">{{item}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"right-icon\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"goods_specifica\" style=\"border: none;color: #171B25;\">\r\n\t\t\t\t\t<view style=\"display: flex;\">\r\n\t\t\t\t\t\t<view style=\"color: #61687C;\">规格</view>\r\n\t\t\t\t\t\t<view style=\"margin-left: 24rpx;display: flex;\">\r\n\t\t\t\t\t\t\t{{Goodsitem.specification}}g\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"right-icon\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"padding: 30rpx 0\">\r\n\t\t\t<u-divider half-width=\"260\" border-color=\"#9FA3B0\" bg-color=\"#F5F5F5\">\r\n\t\t\t\t<image style=\"width: 102rpx;height: 25rpx;\" mode=\"widthFix\" src=\"/static/img/shop/shop_detail_title.png\" alt=\"\" srcset=\"\" />\r\n\t\t\t</u-divider>\r\n\t\t</view>\r\n\t\t<view class=\"goods_detail_concent\">\r\n\t\t\t<rich-text :nodes=\"Goodsitem.goodsDetailInfo\"></rich-text>\r\n\t\t</view>\r\n\t\t<view class=\"goods_detail_footer\">\r\n\t\t\t<view @click=\"goPay\">立即购买</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport number from \"../../../utils/number.js\";\r\n\tconst api = require('../../../config/api');\r\n\tconst util = require('../../../utils/util');\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnumber: number, //声明number属性并赋值为引入的number模块\r\n\t\t\t\tgoodsId: '',\r\n\t\t\t\tGoodsitem: {},\r\n\t\t\t\tshow: false,\r\n\t\t\t\tgoodsNum: 1\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(o) {\r\n\t\t\tthis.goodsId = o.goodsId\r\n\t\t\tthis.getGoodsdetail();\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// this.$nextTick(()=>{\r\n\t\t\t// \tconsole.log(this.$refs.uSwiper)\r\n\t\t\t// })\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoPay() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/indexChild/payment/payment?goodsId=' + this.Goodsitem.id + '&' + 'goodsNum=' +\r\n\t\t\t\t\t\tthis.goodsNum,\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tshowPopup() {\r\n\t\t\t\tthis.show = true\r\n\t\t\t},\r\n\t\t\tasync getGoodsdetail() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '加载中'\r\n\t\t\t\t});\r\n\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\tapi.goodsUrl + '/' + this.goodsId, {},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t);\r\n\t\t\t\tconsole.log(res);\r\n\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tres.data[0].goodsImg = res.data[0].goodsImg.split(',')\r\n\t\t\t\t\tres.data[0].goodsDetailInfo = res.data[0].goodsDetailInfo.replace('max-','')\r\n\t\t\t\t\tres.data[0].goodsDescribe = res.data[0].goodsDescribe?res.data[0].goodsDescribe.split(','):[]\r\n\t\t\t\t\tres.data[0].baozhang = ['品质保障','售后无忧']\r\n\t\t\t\t\tthis.Goodsitem = res.data[0]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.page{\r\n\t\tbackground-color: #F5F5F5;\r\n\t}\r\n\t.goods_content{\r\n\t\tpadding: 24rpx 24rpx 0 24rpx;\r\n\t\t.goods_padding{\r\n\t\t\tpadding: 24rpx;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t}\r\n\t}\r\n\t.goods_name {\r\n\t\tmargin-top: 18rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\t.goods_desc{\r\n\t\tmargin-top: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tcolor: #9FA3B0;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\t.goods_price {\r\n\t\t// padding: 20rpx 0;\r\n\t\tcolor: #FF0046;\r\n\t\tfont-size: 48rpx;\r\n\t}\r\n\r\n\t.goods_price>text:first-child {\r\n\t\tfont-weight: 600;\r\n\t}\r\n\t\r\n\t.goods_params{\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 10rpx;\r\n\t\t.goods_specifica{\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tborder-bottom: 1px solid #E5E6EB;\r\n\t\t\tpadding: 24rpx 0;\r\n\t\t}\r\n\t}\r\n\t.tips_item {\r\n\t\tmargin-top: 12rpx;\r\n\t\twidth: 100rpx;\r\n\t\ttext-align: center;\r\n\t\tpadding: 6rpx 0;\r\n\t\tborder-radius: 4rpx;\r\n\t\tbackground-color: rgba(255, 0, 70, 0.05);\r\n\t\tcolor: #FF0046;\r\n\t\tfont-size: 22rpx;\r\n\t}\r\n\r\n\t.goods_detail_concent {\r\n\t\toverflow-x: hidden;\r\n\t\tpadding-bottom: 170rpx;\r\n\t}\r\n\r\n\t.goods_detail_footer {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 170rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding-top: 6rpx;\r\n\t\tview {\r\n\t\t\theight: 84rpx;\r\n\t\t\twidth: 90%;\r\n\t\t\tborder-radius: 9999px;\r\n\t\t\tbackground-color: #BBA186;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 84rpx;\r\n\t\t}\r\n\t}\r\n\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./GoodsDetails.vue?vue&type=style&index=0&id=5d37e7c3&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./GoodsDetails.vue?vue&type=style&index=0&id=5d37e7c3&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360226782\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}