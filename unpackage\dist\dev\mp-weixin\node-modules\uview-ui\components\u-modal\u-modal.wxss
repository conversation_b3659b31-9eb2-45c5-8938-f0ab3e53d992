@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-model.data-v-713d0fd3 {
  height: auto;
  overflow: hidden;
  font-size: 32rpx;
  background-color: #fff;
}
.u-model__btn--hover.data-v-713d0fd3 {
  background-color: #e6e6e6;
}
.u-model__title.data-v-713d0fd3 {
  padding-top: 48rpx;
  font-weight: 500;
  text-align: center;
  color: #303133;
}
.u-model__content__message.data-v-713d0fd3 {
  padding: 48rpx;
  font-size: 30rpx;
  text-align: center;
  color: #606266;
}
.u-model__footer.data-v-713d0fd3 {
  display: flex;
  flex-direction: row;
}
.u-model__footer__button.data-v-713d0fd3 {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  cursor: pointer;
  text-align: center;
  border-radius: 4rpx;
}
