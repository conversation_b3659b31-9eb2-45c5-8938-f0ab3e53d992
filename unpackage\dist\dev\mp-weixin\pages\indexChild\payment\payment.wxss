
page{
	background-color: #F5F5F5;
}

@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-299f44de {
  padding: 24rpx 24rpx 0 24rpx;
}
.address.data-v-299f44de {
  padding: 30rpx 24rpx;
  background-color: white;
  border-radius: 10rpx;
}
.address_top.data-v-299f44de {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product_info.data-v-299f44de {
  margin-top: 24rpx;
  display: flex;
  padding: 24rpx;
  background-color: #FFFFFF;
  border-radius: 10rpx;
}
.product_info .prodct_left.data-v-299f44de {
  display: flex;
  border-radius: 10rpx;
  overflow: hidden;
}
.product_info .product_center.data-v-299f44de {
  flex: 1;
  margin-left: 20rpx;
}
.product_info .product_right.data-v-299f44de {
  margin-left: 32rpx;
}
.product_info .product_right .font_small.data-v-299f44de {
  font-size: 36rpx;
  color: #171B25;
  font-weight: 600;
}
.inviter.data-v-299f44de {
  margin-top: 24rpx;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 10rpx;
}
.inviter .uni-input.data-v-299f44de {
  font-size: 28rpx;
  text-align: right;
}
.pay_type.data-v-299f44de {
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-radius: 10rpx;
}
.goods_detail_footer.data-v-299f44de {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 170rpx;
  display: flex;
  justify-content: center;
  background-color: #FFFFFF;
  padding-top: 6rpx;
}
.goods_detail_footer .btn.data-v-299f44de {
  height: 84rpx;
  width: 93%;
  border-radius: 9999px;
  background-color: #BBA186;
  color: #FFFFFF;
  font-size: 28rpx;
  text-align: center;
  line-height: 84rpx;
}
