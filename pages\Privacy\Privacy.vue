<template>
    <!-- pages/Privacy/Privacy.wxml -->
    <view>
        <web-view :src="URL" />
    </view>
</template>

<script>
// pages/Privacy/Privacy.js
const api = require('../../config/api');
const util = require('../../utils/util');
export default {
    data() {
        return {
			URL:api.privacyUrl
		};
    },
    onLoad(options) {
		// console.log(api.privacyAgreementUrl);
	},
    onReady() {},
    onShow() {},
    onHide() {},
    onUnload() {},
    onPullDownRefresh() {},
    onReachBottom() {},
    onShareAppMessage() {},
    methods: {}
};
</script>
<style>
	page{
		padding: 15px;
	}
</style>
