
.status_bar {
	height: 25px;
	width: 100%;
}

/* 单选框 */
.manual {
	position: fixed;
	bottom: 100rpx;
	left: 0;
	right: 0;
	font-size: 22rpx;
	color: #9FA3B0;
	padding: 0 80rpx;
	display: flex;
	justify-content: center;
}
.radion {
	align-self: center;
	margin-top: 0rpx;
}
radio .wx-radio-input {
	border-radius: 50%;
	width: 24rpx;
	border: 2rpx solid #5e5e5f;
	height: 24rpx;
}
.login-agree {
	color: #BBA186;
}

@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-891c2434 {
  padding: 1px;
  padding: 0 80rpx;
}
.item.data-v-891c2434 {
  height: 45px;
  margin-top: 15px;
}
.account.data-v-891c2434 {
  display: flex;
  box-sizing: border-box;
  font-size: 30rpx;
  align-items: center;
  border-bottom: 1px solid #F1F2F5;
}
.account input.data-v-891c2434 {
  flex: 1;
  height: 60rpx;
}
/* 短信验证码 */
.phonecode.data-v-891c2434 {
  height: 35px;
  padding: 0px 10px;
  text-align: center;
  line-height: 35px;
  color: #BBA186;
  font-size: 28rpx;
}
.confimr.data-v-891c2434 {
  height: 88rpx;
  font-size: 28rpx;
  margin-top: 80rpx;
  background-color: #BBA186;
  text-align: center;
  color: #fff;
  line-height: 88rpx;
  border-radius: 30px;
}
