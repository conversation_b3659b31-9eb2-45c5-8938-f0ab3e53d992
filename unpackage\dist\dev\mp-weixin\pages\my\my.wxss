
page {
	background-color: #F7F7F7;
}

@charset "UTF-8";
/* uni.scss */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 字体变量 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-0be17cc6 {
  padding: 24rpx;
}
.mh-person.data-v-0be17cc6 {
  display: flex;
  align-items: center;
}
.mh-person .mh-p-img.data-v-0be17cc6 {
  width: 100rpx;
  height: 100rpx;
}
.mh-person .mh-p-img.mh-p-img-bg.data-v-0be17cc6 {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}
.mh-person .mh-p-img.mh-p-img-bg .iconfont.data-v-0be17cc6 {
  font-size: 2.25rem;
  color: #999999;
}
.mh-person .mh-p-center.data-v-0be17cc6 {
  margin-left: 20rpx;
}
.mh-person .mh-p-center .mh-pc-text1.data-v-0be17cc6 {
  font-size: 36rpx;
  font-weight: 600;
  color: #171B25;
}
.mh-person .mh-p-center .mh-pc-text2.data-v-0be17cc6 {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #61687C;
}
.my_order.data-v-0be17cc6 {
  margin-top: 24rpx;
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx 0;
}
.my_order .my_order_title.data-v-0be17cc6 {
  padding: 0 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.my_order .my_order_state.data-v-0be17cc6 {
  display: flex;
  margin-top: 20rpx;
}
.my_order .my_order_state .my_order_list.data-v-0be17cc6 {
  padding-top: 10rpx;
  width: 25%;
}
.my-nav-content.data-v-0be17cc6 {
  margin-top: 24rpx;
  background-color: white;
  border-radius: 14rpx;
}
.my-nav-content .my-nav-item.data-v-0be17cc6 {
  display: flex;
  align-items: center;
  padding-left: 24rpx;
}
.my-nav-content .my-nav-item .my-ni-left.data-v-0be17cc6 {
  flex: 1;
  margin-left: 20rpx;
  padding: 30rpx 30rpx 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #F7F7F7;
}
.my-nav-content .my-nav-item .icon.data-v-0be17cc6 {
  width: 32rpx;
  height: 32rpx;
}
