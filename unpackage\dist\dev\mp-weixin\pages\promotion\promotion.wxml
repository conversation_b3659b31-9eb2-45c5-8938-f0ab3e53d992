<view class="page"><block wx:for="{{storeList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view class="store_content"><view class="store_img"><image style="width:160rpx;height:160rpx;" src="{{item.img}}" alt></image></view><view class="store_name"><view style="margin-top:10rpx;font-size:28rpx;">{{item.name}}</view><view style="display:flex;justify-content:space-between;align-items:center;color:#61687C;font-size:24rpx;margin-top:10rpx;"><view style="display:flex;"><image style="width:28rpx;" mode="widthFix" src="/static/img/shop/shop-clock.png" alt></image><text style="margin-left:10rpx;">{{item.openTime}}</text></view><view style="display:flex;"><view data-event-opts="{{[['tap',[['onCall',['$0'],[[['storeList','id',item.id,'phone']]]]]]]}}" class="store_round" bindtap="__e"><image style="width:28rpx;" mode="widthFix" src="/static/img/shop/shop-calling.png" alt></image></view><view data-event-opts="{{[['tap',[['onStoreInfo',['$0'],[[['storeList','id',item.id]]]]]]]}}" class="store_round" bindtap="__e"><image style="width:28rpx;" mode="widthFix" src="/static/img/shop/shop-user.png" alt></image></view></view></view><view style="display:flex;color:#61687C;font-size:24rpx;margin-top:10rpx;"><image style="width:28rpx;" mode="widthFix" src="/static/img/shop/shop-location.png" alt></image><text style="margin-left:10rpx;">{{item.address}}</text></view></view></view></block><u-loadmore vue-id="27f14ef8-1" margin-top="{{$root.g0?'':50}}" status="{{loadStatus}}" load-text="{{loadText}}" bind:__l="__l"></u-loadmore><u-popup bind:input="__e" vue-id="27f14ef8-2" mode="center" border-radius="{{14}}" length="90%" close-icon-color="#FFFFFF" closeable="{{true}}" value="{{show}}" data-event-opts="{{[['^input',[['__set_model',['','show','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view><view class="gold_new"><view class="gold_new_title">门店信息</view></view><view style="padding:0 32rpx;"><view class="gold_price_show"><view style="font-size:28rpx;">联系人：</view><view><text style="font-size:32rpx;color:#BBA186;">{{storeInfo.userName}}</text></view></view><view class="gold_price_show"><view style="font-size:28rpx;">联系方式：</view><view><text style="font-size:32rpx;color:#BBA186;">{{storeInfo.phone}}</text></view></view></view><view class="goods_detail_footer"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e">我知道了</view></view></view></u-popup></view>