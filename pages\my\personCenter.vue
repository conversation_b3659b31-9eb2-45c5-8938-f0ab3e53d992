<template>
	<view style="padding: 24rpx;">
		<view class="pd-main first">
			<view class="my-nav-item">
				<text class="pd-nav-left">头像</text>
				<view class="my-ni-right" @tap="picUP">
					<image class="my-nav-img" :src="userData.imgUrl" mode="scaleToFill"></image>
					<uni-icons type="right" size="16"></uni-icons>
				</view>
			</view>
			<view class="my-nav-item" @click="onModifyName">
				<text class="pd-nav-left">姓名</text>
				<view class="my-ni-right">
					<text>{{ userData.userName }}</text>
				</view>
			</view>
			<view class="my-nav-item">
				<text class="pd-nav-left">手机号</text>
				<view class="my-ni-right">
					<text>{{ userData.phone }}</text>
				</view>
			</view>
		</view>
		<u-modal v-model="show" :show-cancel-button="true" @confirm="confirm">
			<view style="padding: 24rpx;">
				<u-field v-model="userName" label="姓名" placeholder="请填写姓名"></u-field>
			</view>
		</u-modal>
	</view>
</template>

<script>
	const api = require('../../config/api')
	const util = require('../../utils/util')
	export default {
		name: 'personCenter',
		data() {
			return {
				userData: {
					imgUrl: 'https://pic1.imgdb.cn/item/669a6550d9c307b7e91d31f4.png',
					realname: '',
					phone: '',
					inviteCode: '',
				},
				show: false,
				userName: ''
			}
		},
		onLoad: function(options) {
			this.getUserInfo()
		},
		methods: {
			async getUserInfo() {
				uni.showLoading({
					title: '加载中'
				});
				const res = await util.request(api.getUserInfoUrl, {}, 'POST')
				console.log(res)
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				} else {
					uni.hideLoading();
					this.userData = res.data.user
				}
			},
			async confirm() {
				let that = this
				const res = await util.request(api.modifyNameUrl+`/${that.userData.id}/${that.userName}`, {}, 'GET')
				console.log(res)
				if (res.code !== 0) {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				} else {
					uni.showToast({
						title: '修改成功',
						icon: 'success'
					})
					setTimeout(()=>{
						that.show = false
						that.getUserInfo()
					},600)
				}
			},
			onModifyName() {
				this.show = true
			},
			// 上传图片
			picUP: function(e) {
				var that = this
				uni.chooseImage({
					count: 1, //图片可选择数量
					sizeType: ['compressed'], //original 原图，compressed 压缩图，默认二者都有
					sourceType: ['album', 'camera'], //album 从相册选图，camera 使用相机，默认二者都有。
					extension: ['.png', '.jpg'], // 限制可选择的图片格式
					success: res => {
						that.uploadImg(res.tempFilePaths[0]);
						
						
					},
					fail: res => {
						uni.showToast({
							title: '操作失败',
							icon: 'none',
							duration: 2000,
							mask: true,
							complete: function() {
								console.log(errorMessage);
							}
						});
					},
				});
			},
			uploadImg: function(file) {
				var userToken = uni.getStorageSync('token') ||''
				uni.uploadFile({ //将本地资源上传到开发者服务器
					url: api.uploadUrl, //接口地址
					filePath: file, //图片地址
					// file,
					name: 'test',
					header: {
						"token": userToken
					},
					formData: {
						file: file
					},
					success: (res) => {
						let data = JSON.parse(res.data)
						if (data.code == 0) {
							uni.showToast({
								title: '图片上传成功！'
							});
							uni.showLoading({
								title: '正在更改',
								mask: true
							});
							this.userData.imgUrl = data.data.url;
							this.updateUserInfo(data.data.url);
						} else {
							uni.showToast({
								title: res.msg,
								icon: "error"
							});
						}
					},
					fail(res) {
						console.log(res);
					}
				});
			},
			updateUserInfo(imgUrl) {
				util.request(api.changeUeerPhotoUrl, {img: imgUrl}, 'POST').then((res)=> {
					console.log(res)
					debugger
					if (res.code !== 0) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					} else {
						uni.hideLoading()
						util.showAlertToast('修改成功')
					}
				})
			}
		}
	}
</script>
<style>
	page{
		background-color: #F7F7F7;
	}
</style>
<style lang="scss" scoped>
	.pd-main {
		background-color: #fff;
		border-radius: 10rpx;
		.my-nav-item {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
			border-bottom: 1px solid #F7F7F7;
			.pd-nav-left {
				color: #666666;
			}

			.my-ni-right {
				display: flex;
				flex-direction: row;
				align-items: center;

				.my-nav-img {
					width: 24px;
					height: 24px;
					border-radius: 50%;
				}
			}
		}
	}
</style>