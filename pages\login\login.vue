<template>
	<view class="loginpage">
		<!-- #ifdef APP-PLUS -->
		<view class="status_bar">
		  <!-- 这是状态栏 -->
		</view>
		<!-- #endif -->
		<view class="" style="height: 50px; width: 100%;">
			
		</view>
		<view class="login-img">
			<image style="width: 150px;height: 150px;border-radius: 10px;" src="/static/KLLD.png" mode=""></image>
		</view>
		<view class="login-form">
			<view class="login-account">
				<image src="/static/img/login/ic_phone.png" mode="widthFix" style="width: 15px; height: 15px;"></image>
				<input placeholder="请输入手机号" type="number" v-model="loginForm.phone" />
			</view>
			<view class="login-account">
				<image src="/static/img/login/ic_password.png" mode="widthFix" style="width: 15px; height: 15px;"></image>
				<input type="password" placeholder="请输入密码"  v-model="loginForm.passwd" v-if="passwordtype == 'password'" />
				<input type="text" placeholder="请输入密码"  v-model="loginForm.passwd" v-else />
				
				<image class="showOre" src="/static/img/login/ic_password_normal.png" mode="widthFix" style="width: 15px; height: 10px;" @click="sowPasst"  v-if="passwordtype == 'password' "></image>
				<image class="showOre" src="/static/img/login/ic_password_press.png" mode="widthFix" style="width: 15px; height: 10px;" @click="hideenPasst" v-else ></image>
			</view>
			<view class="footer">
				<text  @click="gozhuce">立即注册</text>
				<text  @click="goForgotPassword">忘记密码</text>
			</view>
		</view>
		<view class="authorized-btn" @click="Login">
			登录
		</view>
	</view>
</template>

<script>
	const api = require('../../config/api');
	const util = require('../../utils/util');
	export default {
		data() {
			return {
				loginForm: {
					passwd: "",
					phone: "",
					passwd: '17753422520',
					phone: '15358381598',
					 // "passwd": "17753422520",
					 //  "phone": "18534567716",
					tenantId: '3'
				},
				// passwd: "17753422520",
				// phone: "18453511319",
				// passwd: '17753422520',
				 // phone: '13603543256',
				// tenantId: '3'
				passwordtype:"password"
			}
		},
		onLoad() {

		},
		methods: {
			gozhuce(){
				uni.navigateTo({
					url: '/pages/register/register'
				});
			},
			hideenPasst(){
				this.passwordtype = "password"
				
			},
			sowPasst(){
				this.passwordtype = "number"
			},
			// 忘记密码
			goForgotPassword() {

				uni.navigateTo({
					url: '/pages/ForgotPassword/ForgotPassword'
				});
			},
			Login() {
				uni.showLoading({
					title: '正在登录'
				});
				this.handleLogin()
			},
			Login() {
				uni.showLoading({
					title: '正在登录'
				});
				this.handleLogin()
			},
			// 登录
			async handleLogin() {
				if (!this.loginForm.phone.trim()) {
					uni.showToast({
						title: '手机号不能为空',
						icon: 'none',
					})
					return;
				}
				if (!this.loginForm.passwd.trim()) {
					uni.showToast({
						title: '密码不能空',
						icon: 'none'
					});
					return;
				}
				// try {
					const res = await util.request(
						api.LoginUrl, {
							passwd: this.loginForm.passwd,
							phone: this.loginForm.phone,
							tenantId: this.loginForm.tenantId
						},
						'POST'
					);
					if (res.code !== 0) {
						uni.hideLoading();
						setTimeout(() => {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}, 30)

						return;
					} else {
							uni.setStorageSync('token', res.data.token)
							if(res.data.user.phone =='17753422520'){
								uni.setStorageSync('isTest', true)
								uni.setTabBarItem({
									index: 2,
									text: "消息"
								});
								uni.setTabBarItem({
									index: 1,
									text: "文案"
								});
							}else{
								uni.setStorageSync('isTest', false)
							}
							
					// 	uni.setStorageSync('phone',this.loginForm.phone);
					
					// 	uni.setStorageSync('userid', res.user.id);
					// 	uni.setStorageSync('user', res.user);
					// 	uni.setStorageSync('agentId', res.user.agentId);
						
					// 	uni.setStorageSync('merchantId', res.user.merchantId)
					// 	uni.hideLoading();
						setTimeout(() => { 
							uni.showToast({
								title: "登陆成功",
								icon: 'none'
							});
							
							uni.switchTab({
							    url: '/pages/index/index'
							});
						}, 30)
						
						
						
						// this.getmerDetail();
					}
			},
		}
	}
</script>


<style lang="scss" scoped>
.loginpage{
	padding: 2px;
	
}
	.login-img {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 15px;
	}

	/* 表单部分 */
	.login-form {
		margin-left: 25px;
		margin-right: 25px;
		margin-top: 25px;
	}

	.login-account {
		position: relative;
		display: flex;
		align-items: center;
		font-size: 13px;
		align-items: center;
		background-color: #fff;
		height: 45px;
		border-bottom: 1px solid #E5E5E5;
	}

	.login-account input {
		flex: 1;
		margin-left: 15px;
		padding: 5px;
		padding-right: 15px;
	}

	.inp-palcehoder {
		font-size: 13px;
	}

	.input-item {
		position: relative;
		margin-left: 20px;
		width: 40px;
	}

	.footer {
		font-size: 14px;
		margin-top: 20px;
		display: flex;
		justify-content: space-between;
	}

	.authorized-btn {
		margin-top: 15px;
		margin-left: 15px;
		margin-right: 15px;
		// padding: 10px;
		height: 44px;
		line-height: 44px;
		color: #fff;
		// margin: 0 auto;
		text-align: center;
		background-color: #6A9FFB;
		border-radius: 20px;
	}
	.showOre{
		// position: absolute;
		// right: 10px;
		// top: 50%;
		// 	transform: translateY(-50%);
	}
	.footer{
		color: #76A7FB;
		font-size: 15px;
	}

</style>